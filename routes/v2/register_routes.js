const express = require("express");
const router = express.Router();
const { registerNewUser, checkUserExist,changeUserPassword } = require("../../controller/v2/register_controller");
const validateRequest = require("../../middleware/validateRequest");
const createUserSchema = require("../../validator/userValidator.js");
router.post("/",validateRequest(createUserSchema), registerNewUser);
router.post("/changeUserPassword", changeUserPassword);
router.get("/userExist", checkUserExist);

module.exports = router;