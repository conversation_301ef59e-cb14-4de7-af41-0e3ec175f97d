// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "debian-openssl-1.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

enum Role {
  USER
  ADMIN
}

enum VoteType {
  UPVOTE
  DOWNVOTE
}

model Lesson {
  id               String             @id @default(uuid())
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  title            String
  lessonTitle      String
  lessonNumber     Int                @unique
  paragraph        String             @db.Text
  part             Int
  contentType      Int
  picture          String?
  pressName        String
  publishDate      String
  audio            String?            @db.Text
  category         String
  QuizOne          QuizOne[]
  QuizTwo          QuizTwo[]
  QuizThree        QuizThree[]
  LessonDiscussion LessonDiscussion[]
}

model QuizOne {
  id       String @id @default(uuid())
  question String
  option1  String
  option2  String
  option3  String
  option4  String
  answer   String
  lesson   Lesson @relation(fields: [lessonId], references: [lessonNumber])
  lessonId Int
}

model QuizTwo {
  id       String @id @default(uuid())
  question String
  answer   String
  lesson   Lesson @relation(fields: [lessonId], references: [lessonNumber])
  lessonId Int
}

model QuizThree {
  id       String @id @default(uuid())
  question String
  option1  String
  option2  String
  option3  String
  option4  String
  answer   String
  lesson   Lesson @relation(fields: [lessonId], references: [lessonNumber])
  lessonId Int
}

model User {
  id                  String               @id @default(uuid())
  name                String
  photo               String?
  password            String
  phone               String               @unique
  refreshToken        String?              @unique
  createdAt           DateTime?            @default(now())
  updatedAt           DateTime?            @updatedAt
  SubscriptionRequest SubscriptionRequest?
  role                UserRole[]
  LessonDiscussion    LessonDiscussion[]
  ScoreBoard          ScoreBoard?
  Vote                Vote[]
}

model UserRole {
  id     String @id @default(uuid())
  user   User   @relation(fields: [userId], references: [id])
  userId String
  role   Role   @default(USER)
}

model SubscriptionRequest {
  id               String   @id @default(uuid())
  subscrioberPhone String
  user             User     @relation(fields: [userId], references: [id])
  userId           String   @unique
  amount           String
  duration         String
  subscribed       Boolean
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
}

model LessonDiscussion {
  id        String             @id @default(uuid())
  lesson    Lesson             @relation(fields: [lessonId], references: [id])
  lessonId  String
  user      User               @relation(fields: [userId], references: [id])
  userId    String
  comment   String
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  parentId  String?
  parent    LessonDiscussion?  @relation("Replies", fields: [parentId], references: [id])
  replies   LessonDiscussion[] @relation("Replies")
  votes     Vote[]
}

model Vote {
  id           String           @id @default(uuid())
  user         User             @relation(fields: [userId], references: [id])
  userId       String
  discussion   LessonDiscussion @relation(fields: [discussionId], references: [id])
  discussionId String
  voteType     VoteType

  @@unique([userId, discussionId])
}

model ScoreBoard {
  id     String @id @default(uuid())
  score  Int
  user   User   @relation(fields: [userId], references: [id])
  userId String @unique
}

model GreLesson {
  id           String      @id @default(uuid())
  createdAt    DateTime    @default(now())
  updatedAt    DateTime    @updatedAt
  title        String
  lessonNumber Int         @unique
  content      String      @db.Text
  GreQuizes    GreQuizes[]
}

model GreQuizes {
  id       String    @id @default(uuid())
  question String
  option1  String
  option2  String
  option3  String
  option4  String
  option5  String
  answer   String
  lesson   GreLesson @relation(fields: [lessonId], references: [id])
  lessonId String
}

model ExamLesson {
  id           String       @id @default(uuid())
  createdAt    DateTime     @default(now())
  updatedAt    DateTime     @updatedAt
  title        String
  lessonNumber Int          @unique
  content      String       @db.Text
  ExamQuizes   ExamQuizes[]
}

model ExamQuizes {
  id       String     @id @default(uuid())
  question String
  option1  String
  option2  String
  option3  String
  option4  String
  answer   String
  lesson   ExamLesson @relation(fields: [lessonId], references: [id])
  lessonId String
}

model Promotion {
  id        String   @id @default(uuid())
  name      String
  photo     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model IetsBook {
  id               String             @id @default(uuid())
  name             String
  subtitle         String
  createdAt        DateTime           @default(now())
  updatedAt        DateTime           @updatedAt
  IeltsReadingList IeltsReadingList[]
}

model IeltsReadingList {
  id                  String                @id @default(uuid())
  name                String
  ieltsBook           IetsBook              @relation(fields: [ieltsBookId], references: [id])
  ieltsBookId         String
  createdAt           DateTime              @default(now())
  updatedAt           DateTime              @updatedAt
  IeltsReadingPassage IeltsReadingPassage[]
}

model IeltsReadingPassage {
  id                         String                       @id @default(uuid())
  name                       String
  ieltsRadingList            IeltsReadingList             @relation(fields: [ieltsRadingListId], references: [id])
  ieltsRadingListId          String
  createdAt                  DateTime                     @default(now())
  updatedAt                  DateTime                     @updatedAt
  IeltsReadingPassageQuizOne IeltsReadingPassageQuizOne[]
  IeltsReadingPassageQuizTwo IeltsReadingPassageQuizTwo[]
  Passage                    Passage[]
  IeltsReadingPassageExample IeltsReadingPassageExample[]
}

model IeltsReadingPassageExample {
  id                    String              @id @default(uuid())
  word                  String
  synonym               String
  antonym               String
  ieltsReadingPassage   IeltsReadingPassage @relation(fields: [ieltsReadingPassageId], references: [id])
  ieltsReadingPassageId String
}

model IeltsReadingPassageQuizOne {
  id                    String              @id @default(uuid())
  question              String
  option1               String
  answer                String
  ieltsReadingPassage   IeltsReadingPassage @relation(fields: [ieltsReadingPassageId], references: [id])
  ieltsReadingPassageId String
}

model IeltsReadingPassageQuizTwo {
  id                    String              @id @default(uuid())
  question              String
  option1               String
  answer                String
  ieltsReadingPassage   IeltsReadingPassage @relation(fields: [ieltsReadingPassageId], references: [id])
  ieltsReadingPassageId String
}

model Passage {
  id                    String              @id @default(uuid())
  title                 String
  paragraph             String              @db.Text
  audio                 String?             @db.Text
  ieltsReadingPassage   IeltsReadingPassage @relation(fields: [ieltsReadingPassageId], references: [id])
  ieltsReadingPassageId String              @unique
  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt
}
