const prisma = require("../../prisma/index");
const { uploadFiles } = require("../../utils/uploadFiles");

/**
 * Promotions Controller
 * Handles CRUD operations for promotional content
 */

const createPromotion = async (req, res) => {
  try {
    const { name } = req.body;
    const files = req.files;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: "Promotion name is required",
      });
    }

    if (!files || !files.photo) {
      return res.status(400).json({
        success: false,
        message: "Promotion photo is required",
      });
    }

    const fileNames = uploadFiles(files);

    const promotion = await prisma.promotion.create({
      data: {
        name,
        photo: fileNames.photo,
      },
    });

    res.status(201).json({
      success: true,
      message: "Promotion created successfully",
      data: promotion,
    });
  } catch (error) {
    console.error("Error creating promotion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create promotion",
      error: error.message,
    });
  }
};

const getPromotions = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const [promotions, totalCount] = await Promise.all([
      prisma.promotion.findMany({
        orderBy: { createdAt: 'desc' },
        skip,
        take: limitNum,
      }),
      prisma.promotion.count(),
    ]);

    res.status(200).json({
      success: true,
      data: promotions,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
      },
    });
  } catch (error) {
    console.error("Error fetching promotions:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch promotions",
      error: error.message,
    });
  }
};

const getPromotionById = async (req, res) => {
  try {
    const { id } = req.params;

    const promotion = await prisma.promotion.findUnique({
      where: { id },
    });

    if (!promotion) {
      return res.status(404).json({
        success: false,
        message: "Promotion not found",
      });
    }

    res.status(200).json({
      success: true,
      data: promotion,
    });
  } catch (error) {
    console.error("Error fetching promotion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch promotion",
      error: error.message,
    });
  }
};

const updatePromotion = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;
    const files = req.files;

    const existingPromotion = await prisma.promotion.findUnique({
      where: { id },
    });

    if (!existingPromotion) {
      return res.status(404).json({
        success: false,
        message: "Promotion not found",
      });
    }

    let updateData = {};
    if (name) updateData.name = name;

    if (files && files.photo) {
      const fileNames = uploadFiles(files);
      updateData.photo = fileNames.photo;
    }

    const promotion = await prisma.promotion.update({
      where: { id },
      data: updateData,
    });

    res.status(200).json({
      success: true,
      message: "Promotion updated successfully",
      data: promotion,
    });
  } catch (error) {
    console.error("Error updating promotion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update promotion",
      error: error.message,
    });
  }
};

const deletePromotion = async (req, res) => {
  try {
    const { id } = req.params;

    const existingPromotion = await prisma.promotion.findUnique({
      where: { id },
    });

    if (!existingPromotion) {
      return res.status(404).json({
        success: false,
        message: "Promotion not found",
      });
    }

    await prisma.promotion.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Promotion deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting promotion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete promotion",
      error: error.message,
    });
  }
};

module.exports = {
  createPromotion,
  getPromotions,
  getPromotionById,
  updatePromotion,
  deletePromotion,
};
