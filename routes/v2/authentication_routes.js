const express = require("express");
const {
  handleLogin,
  sendOtp,
  verifyOtp,
  testServer,
  newAccessToken,
  handleLoginAdmin,
} = require("../../controller/v2/authentication_controller");
const { validateBody } = require("../../middleware/validateRequest");
const { authLimiter, otpLimiter } = require("../../middleware/rateLimiter");
const {
  loginSchema,
  otpRequestSchema,
  otpVerificationSchema,
  refreshTokenSchema,
} = require("../../validators/authValidators");

const router = express.Router();

/**
 * @route   GET /api/v2/auth/health
 * @desc    Test server health
 * @access  Public
 */
router.get("/health", testServer);

/**
 * @route   POST /api/v2/auth/login
 * @desc    User login
 * @access  Public
 * @body    { phone: string, password: string, token?: string }
 */
router.post("/login", authLimiter, validateBody(loginSchema), handleLogin);

/**
 * @route   POST /api/v2/auth/admin/login
 * @desc    Admin login
 * @access  Public
 * @body    { phone: string, password: string, token?: string }
 */
router.post(
  "/admin/login",
  authLimiter,
  validateBody(loginSchema),
  handleLoginAdmin
);

/**
 * @route   POST /api/v2/auth/otp/send
 * @desc    Send OTP to phone number
 * @access  Public
 * @body    { phone: string }
 */
router.post("/otp/send", otpLimiter, validateBody(otpRequestSchema), sendOtp);

/**
 * @route   POST /api/v2/auth/otp/verify
 * @desc    Verify OTP
 * @access  Public
 * @body    { phone: string, enteredOtp: string, hashedOtp: string }
 */
router.post(
  "/otp/verify",
  authLimiter,
  validateBody(otpVerificationSchema),
  verifyOtp
);

/**
 * @route   POST /api/v2/auth/token/refresh
 * @desc    Refresh access token
 * @access  Public
 * @body    { refreshToken: string }
 */
router.post("/token/refresh", validateBody(refreshTokenSchema), newAccessToken);

module.exports = router;
