const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");
const { verifyRoles } = require("../../middleware/verifyRoles");
const { validateBody, validateQuery, validateParams } = require("../../middleware/validateRequest");
const Joi = require("joi");

const {
  createGreLesson,
  getGreLessons,
  getGreLessonById,
  updateGreLesson,
  deleteGreLesson,
  createGreQuiz,
  getGreQuizzesByLessonId,
  updateGreQuiz,
  deleteGreQuiz,
} = require("../../controller/v2/gre-lessons.controller");

// Validation schemas
const createGreLessonSchema = Joi.object({
  title: Joi.string().required().min(1).max(200),
  content: Joi.string().required().min(1),
  lessonNumber: Joi.number().integer().positive().required(),
});

const updateGreLessonSchema = Joi.object({
  title: Joi.string().min(1).max(200),
  content: Joi.string().min(1),
  lessonNumber: Joi.number().integer().positive(),
});

const createGreQuizSchema = Joi.object({
  question: Joi.string().required().min(1),
  option1: Joi.string().required().min(1),
  option2: Joi.string().required().min(1),
  option3: Joi.string().required().min(1),
  option4: Joi.string().required().min(1),
  option5: Joi.string().required().min(1),
  answer: Joi.string().required().min(1),
  lessonId: Joi.string().required(),
});

const updateGreQuizSchema = Joi.object({
  question: Joi.string().min(1),
  option1: Joi.string().min(1),
  option2: Joi.string().min(1),
  option3: Joi.string().min(1),
  option4: Joi.string().min(1),
  option5: Joi.string().min(1),
  answer: Joi.string().min(1),
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().positive().default(1),
  limit: Joi.number().integer().positive().max(100).default(10),
});

const idSchema = Joi.object({
  id: Joi.string().required(),
});

const lessonIdSchema = Joi.object({
  lessonId: Joi.string().required(),
});

// GRE Lessons Routes
/**
 * @route   GET /api/v2/gre-lessons
 * @desc    Get all GRE lessons with pagination
 * @access  Public
 * @query   ?page=1&limit=10
 */
router.get("/", validateQuery(paginationSchema), getGreLessons);

/**
 * @route   POST /api/v2/gre-lessons
 * @desc    Create a new GRE lesson
 * @access  Private (Admin only)
 */
router.post(
  "/",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateBody(createGreLessonSchema),
  createGreLesson
);

/**
 * @route   GET /api/v2/gre-lessons/:id
 * @desc    Get a specific GRE lesson by ID
 * @access  Public
 */
router.get("/:id", validateParams(idSchema), getGreLessonById);

/**
 * @route   PUT /api/v2/gre-lessons/:id
 * @desc    Update a specific GRE lesson
 * @access  Private (Admin only)
 */
router.put(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(idSchema),
  validateBody(updateGreLessonSchema),
  updateGreLesson
);

/**
 * @route   DELETE /api/v2/gre-lessons/:id
 * @desc    Delete a specific GRE lesson
 * @access  Private (Admin only)
 */
router.delete(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(idSchema),
  deleteGreLesson
);

// GRE Quizzes Routes
/**
 * @route   GET /api/v2/gre-lessons/:lessonId/quizzes
 * @desc    Get all quizzes for a specific GRE lesson
 * @access  Public
 */
router.get(
  "/:lessonId/quizzes",
  validateParams(lessonIdSchema),
  getGreQuizzesByLessonId
);

/**
 * @route   POST /api/v2/gre-lessons/:lessonId/quizzes
 * @desc    Create a new quiz for a GRE lesson
 * @access  Private (Admin only)
 */
router.post(
  "/:lessonId/quizzes",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(lessonIdSchema),
  validateBody(createGreQuizSchema),
  (req, res, next) => {
    // Add lessonId from params to body
    req.body.lessonId = req.params.lessonId;
    next();
  },
  createGreQuiz
);

/**
 * @route   PUT /api/v2/gre-lessons/:lessonId/quizzes/:id
 * @desc    Update a specific GRE quiz
 * @access  Private (Admin only)
 */
router.put(
  "/:lessonId/quizzes/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(Joi.object({
    lessonId: Joi.string().required(),
    id: Joi.string().required(),
  })),
  validateBody(updateGreQuizSchema),
  updateGreQuiz
);

/**
 * @route   DELETE /api/v2/gre-lessons/:lessonId/quizzes/:id
 * @desc    Delete a specific GRE quiz
 * @access  Private (Admin only)
 */
router.delete(
  "/:lessonId/quizzes/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(Joi.object({
    lessonId: Joi.string().required(),
    id: Joi.string().required(),
  })),
  deleteGreQuiz
);

module.exports = router;
