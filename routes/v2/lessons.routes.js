const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");
const { verifyRoles } = require("../../middleware/verifyRoles");
const { validateBody, validateQuery, validateParams } = require("../../middleware/validateRequest");
const Joi = require("joi");

const {
  createLesson,
  getLessons,
  getLessonById,
  updateLesson,
  deleteLesson,
} = require("../../controller/v2/lessons.controller");

// Validation schemas
const createLessonSchema = Joi.object({
  lessonTitle: Joi.string().required().min(1).max(200),
  title: Joi.string().required().min(1).max(200),
  lessonNumber: Joi.number().integer().positive().required(),
  paragraph: Joi.string().required().min(1),
  part: Joi.number().integer().positive().required(),
  pressName: Joi.string().required().min(1).max(100),
  publishDate: Joi.string().required(),
  contentType: Joi.number().integer().positive().required(),
  category: Joi.string().required().min(1).max(50),
});

const updateLessonSchema = Joi.object({
  lessonTitle: Joi.string().min(1).max(200),
  title: Joi.string().min(1).max(200),
  lessonNumber: Joi.number().integer().positive(),
  paragraph: Joi.string().min(1),
  part: Joi.number().integer().positive(),
  pressName: Joi.string().min(1).max(100),
  publishDate: Joi.string(),
  contentType: Joi.number().integer().positive(),
  category: Joi.string().min(1).max(50),
});

const getLessonsQuerySchema = Joi.object({
  contentType: Joi.number().integer().positive(),
  category: Joi.string().max(50),
  part: Joi.number().integer().positive(),
  page: Joi.number().integer().positive().default(1),
  limit: Joi.number().integer().positive().max(100).default(10),
});

const lessonIdSchema = Joi.object({
  id: Joi.number().integer().positive().required(),
});

/**
 * @route   GET /api/v2/lessons
 * @desc    Get all lessons with optional filtering and pagination
 * @access  Public
 * @query   ?contentType=1&category=news&part=1&page=1&limit=10
 */
router.get("/", validateQuery(getLessonsQuerySchema), getLessons);

/**
 * @route   POST /api/v2/lessons
 * @desc    Create a new lesson
 * @access  Private (Admin only)
 */
router.post(
  "/",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateBody(createLessonSchema),
  createLesson
);

/**
 * @route   GET /api/v2/lessons/:id
 * @desc    Get a specific lesson by ID
 * @access  Public
 */
router.get("/:id", validateParams(lessonIdSchema), getLessonById);

/**
 * @route   PUT /api/v2/lessons/:id
 * @desc    Update a specific lesson
 * @access  Private (Admin only)
 */
router.put(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(lessonIdSchema),
  validateBody(updateLessonSchema),
  updateLesson
);

/**
 * @route   DELETE /api/v2/lessons/:id
 * @desc    Delete a specific lesson
 * @access  Private (Admin only)
 */
router.delete(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(lessonIdSchema),
  deleteLesson
);

module.exports = router;
