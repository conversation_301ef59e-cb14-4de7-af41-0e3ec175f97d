const prisma = require("../prisma/index");
const { uploadFiles, removeFile } = require("../utils/uploadFiles");
const addSingleLesson = async (req, res) => {

    const { lessonTitle, title, lessonNumber, paragraph, part, pressName, publishDate, contentType, category } = req.body;


    console.log(quizTwoData);
    if (!lessonTitle || !title || !lessonNumber || !paragraph || !part || !pressName || !publishDate || !category) {
        return res
            .status(400)
            .json({ message: "plase provide valid credentials" });
    }

    try {
        const lesson = await prisma.lesson.create({
            data: {
                lessonTitle: lessonTitle,
                lessonNumber: lessonNumber,
                paragraph: paragraph,
                pressName: pressName,
                part: part,
                publishDate: publishDate,
                title: title,
                contentType: contentType,
                category: category
            }
        });


        res.status(200).json({ message: `New lesson ${lesson.lessonNumber} created`, })
    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: err });
    }
};


const getAllLesson = async (req, res) => {
    const { contentType, category, part } = req.query;
    let whereClause = {};

    if (contentType) whereClause.contentType = parseInt(contentType);
    if (part) whereClause.part = parseInt(part);
    if (category) whereClause.category = category;

    try {
        const lesson = await prisma.lesson.findMany({
            where: whereClause
        });
        res.status(200).json({ message: lesson });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err });
    }
}


const getAllLessonByPage = async (req, res) => {
    const { limit, page } = req.query;
    
    // Parse limit and page query parameters
    const limitValue = parseInt(limit) || 10;  // Default limit to 10 if not provided
    const pageValue = parseInt(page) || 1;  // Default page to 1 if not provided

    // Calculate the offset
    const offset = (pageValue - 1) * limitValue;

    try {
        const lessons = await prisma.lesson.findMany({
            skip: offset,
            take: limitValue,
        });

        // Get the total count of lessons for pagination info
        const totalLessons = await prisma.lesson.count();
        
        // Calculate total pages
        const totalPages = Math.ceil(totalLessons / limitValue);

        res.status(200).json({ 
            message: lessons,
            pagination: {
                totalLessons,
                totalPages,
                currentPage: pageValue,
                limit: limitValue,
            }
        });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
}



const getLessonDetailsData = async (req, res) => {
    const { lessonId } = req.query;
    const lessonIdInt = parseInt(lessonId);
    try {
        const lesson = await prisma.lesson.findUnique({
            where: {
                id: lessonIdInt
            },
            include: {
                QuizOne: true,
                QuizTwo: true,
                QuizThree: true,
            }
        });
        res.status(200).json({ message: lesson, })

    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: err });

    }
}


const getExamQuizListByLesson = async (req, res) => {
    const { lessonNumber } = req.query;

    try {
        const lesson = await prisma.examQuizes.findMany({
            where: {
                lessonNumber: parseInt(lessonNumber)
            },
        });
        res.status(200).json({ message: lesson, })

    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: err });

    }
}



const getGreQuizListByLesson = async (req, res) => {
    const { lessonNumber } = req.query;

    try {
        const lesson = await prisma.greQuizes.findMany({
            where: {
                lessonNumber: parseInt(lessonNumber)
            },
        });
        res.status(200).json({ message: lesson, })

    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: err });

    }
}



const addPromotion = async (req, res) => {
    const { name } = req.body;
  const files = req.files;
  
    // Validate required fields
    if (!name) {
        return res.status(400).json({ message: "All required fields must be provided" });
    }

    try {
          const fileNames = uploadFiles(files);
        // Create a new promotion
        const newPromotion = await prisma.promotion.create({
            data: {
                name,
                photo:fileNames.photo
            }
        });

        // Respond with the newly created promotion
        res.status(201).json({ message: newPromotion });
    } catch (err) {
        console.error("Error adding promotion:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};



const getAllPromotion = async (req, res) => {

    try {
        const promotion = await prisma.promotion.findMany({
        });
        res.status(200).json({ message: promotion, })

    }
    catch (err) {
        console.error(err);
        res.status(500).json({ message: err });

    }
}







const addQuizOne = async (req, res) => {
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    try {
        const newQuizOne = await prisma.quizOne.create({
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                answer,
                lessonId
            }
        });
        res.status(201).json({ message: "QuizOne added successfully", data: newQuizOne });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};


const editQuizOne = async (req, res) => {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    try {
        const updatedQuizOne = await prisma.quizOne.update({
            where: { id: parseInt(id) },
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                answer,
                lessonId
            }
        });
        res.status(200).json({ message: "QuizOne updated successfully", data: updatedQuizOne });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};


const deleteQuizOne = async (req, res) => {
    const { id } = req.params;

    try {
        await prisma.quizOne.delete({
            where: { id: parseInt(id) }
        });
        res.status(200).json({ message: "QuizOne deleted successfully" });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const addQuizTwo = async (req, res) => {
    const { question, answer, lessonId } = req.body;

    try {
        const newQuizTwo = await prisma.quizTwo.create({
            data: {
                question,
                answer,
                lessonId
            }
        });
        res.status(201).json({ message: "QuizTwo added successfully", data: newQuizTwo });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const editQuizTwo = async (req, res) => {
    const { id } = req.params;
    const { question, answer, lessonId } = req.body;

    try {
        const updatedQuizTwo = await prisma.quizTwo.update({
            where: { id: parseInt(id) },
            data: {
                question,
                answer,
                lessonId
            }
        });
        res.status(200).json({ message: "QuizTwo updated successfully", data: updatedQuizTwo });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const deleteQuizTwo = async (req, res) => {
    const { id } = req.params;

    try {
        await prisma.quizTwo.delete({
            where: { id: parseInt(id) }
        });
        res.status(200).json({ message: "QuizTwo deleted successfully" });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const addQuizThree = async (req, res) => {
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    try {
        const newQuizThree = await prisma.quizThree.create({
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                answer,
                lessonId
            }
        });
        res.status(201).json({ message: "QuizThree added successfully", data: newQuizThree });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const editQuizThree = async (req, res) => {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    try {
        const updatedQuizThree = await prisma.quizThree.update({
            where: { id: parseInt(id) },
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                answer,
                lessonId
            }
        });
        res.status(200).json({ message: "QuizThree updated successfully", data: updatedQuizThree });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};




const deleteQuizThree = async (req, res) => {
    const { id } = req.params;

    try {
        await prisma.quizThree.delete({
            where: { id: parseInt(id) }
        });
        res.status(200).json({ message: "QuizThree deleted successfully" });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};

















// exam quiz 


const addExamQuiz = async (req, res) => {
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;
    const lessonNumber=lessonId;

    try {
        const newQuizThree = await prisma.examQuizes.create({
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                answer,
                lessonNumber
            }
        });
        res.status(201).json({ message: "QuizThree added successfully", data: newQuizThree });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const editExamQuiz = async (req, res) => {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    try {
        const updatedQuizThree = await prisma.examQuizes.update({
            where: { id: parseInt(id) },
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                answer,
                lessonId
            }
        });
        res.status(200).json({ message: "QuizThree updated successfully", data: updatedQuizThree });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};




const deleteExamQuiz = async (req, res) => {
    const { id } = req.params;

    try {
        await prisma.examQuizes.delete({
            where: { id: parseInt(id) }
        });
        res.status(200).json({ message: "QuizThree deleted successfully" });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};












//gre questions





const addGreQuiz = async (req, res) => {
    const { question, option1, option2, option3, option4, option5,answer, lessonId } = req.body;
 const lessonNumber=lessonId;
    try {
        const newQuizThree = await prisma.greQuizes.create({
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                option5,
                answer,
                lessonNumber
            }
        });
        res.status(201).json({ message: "QuizThree added successfully", data: newQuizThree });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const editGreQuiz = async (req, res) => {
    const { id } = req.params;
    const { question, option1, option2, option3, option4,option5, answer, lessonId } = req.body;
 const lessonNumber=lessonId;
    try {
        const updatedQuizThree = await prisma.greQuizes.update({
            where: { id: parseInt(id) },
            data: {
                question,
                option1,
                option2,
                option3,
                option4,
                option5,
                answer,
                lessonNumber
            }
        });
        res.status(200).json({ message: "QuizThree updated successfully", data: updatedQuizThree });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};




const deleteGreQuiz = async (req, res) => {
    const { id } = req.params;

    try {
        await prisma.greQuizes.delete({
            where: { id: parseInt(id) }
        });
        res.status(200).json({ message: "QuizThree deleted successfully" });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};





//




const getAllExamQuizByLessonId = async (req, res) => {
    const { lessonId } = req.params;

    try {
        const quizOnes = await prisma.examQuizes.findMany({
            where: { lessonNumber: parseInt(lessonId) }
        });
        res.status(200).json({ message: "QuizOnes retrieved successfully", data: quizOnes });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};

const getAllGreQuizesByLessonId = async (req, res) => {
    const { lessonId } = req.params;

    try {
        const quizOnes = await prisma.greQuizes.findMany({
            where: { lessonNumber: parseInt(lessonId) }
        });
        res.status(200).json({ message: "QuizOnes retrieved successfully", data: quizOnes });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};






const getAllQuizOneByLessonId = async (req, res) => {
    const { lessonId } = req.params;

    try {
        const quizOnes = await prisma.quizOne.findMany({
            where: { lessonId: parseInt(lessonId) }
        });
        res.status(200).json({ message: "QuizOnes retrieved successfully", data: quizOnes });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};




const getAllQuizTwoByLessonId = async (req, res) => {
    const { lessonId } = req.params;

    try {
        const quizTwos = await prisma.quizTwo.findMany({
            where: { lessonId: parseInt(lessonId) }
        });
        res.status(200).json({ message: "QuizTwos retrieved successfully", data: quizTwos });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};



const getAllQuizThreeByLessonId = async (req, res) => {
    const { lessonId } = req.params;

    try {
        const quizThrees = await prisma.quizThree.findMany({
            where: { lessonId: parseInt(lessonId) }
        });
        res.status(200).json({ message: "QuizThrees retrieved successfully", data: quizThrees });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};


const addQuizDiscussion = async (req, res) => {
    const { user,comment,lesson } = req.body;
    
    if(!user||!comment||!lesson)
    {
            res.status(400).json({ error:"Pass valid data" });
    }

    try {
        const userId=parseInt(user);
        const lessonId=parseInt(lesson);
        
        const newQuizThree = await prisma.lessonDiscussion.create({
            data: {
                userId,
                comment,
                lessonId,
            }
        });
        res.status(201).json({ message: "Comment added", });
    } catch (err) {
        console.error(err);
        res.status(500).json({ message: err.message });
    }
};

const getQuizDiscussion = async (req, res) => {
  const { lessonId, perPage, pageNumber } = req.query;

  try {
    // Convert query params to numbers and set defaults
    const limit = parseInt(perPage) || 10;
    const page = parseInt(pageNumber) || 1;

    // Validate inputs
    if (page < 1 || limit < 1) {
      return res.status(400).json({ message: "Invalid pagination parameters" });
    }

    // Fetch the total count of discussions for pagination calculation
    const totalItems = await prisma.lessonDiscussion.count({
      where: { lessonId: parseInt(lessonId) },
    });

    const totalPages = Math.ceil(totalItems / limit);

    // Ensure the requested page is within valid range
    if (page > totalPages && totalPages > 0) {
      return res.status(400).json({ message: "Page number out of range" });
    }

    // Fetch discussions with pagination and sorting
    const discussions = await prisma.lessonDiscussion.findMany({
      where: { lessonId: parseInt(lessonId) },
      include: { user: true }, // Include related user information
      orderBy: { createdAt: "desc" },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Respond with paginated data
    res.status(200).json({
      data: discussions,
      totalItems,
      totalPages,
      currentPage: page,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};








module.exports = {
    addSingleLesson, getAllLesson, getLessonDetailsData,getExamQuizListByLesson,getAllPromotion,addPromotion,getAllLessonByPage,
    addQuizOne,editQuizOne,deleteQuizOne,addQuizTwo,editQuizTwo,deleteQuizTwo,addQuizThree,editQuizThree,deleteQuizThree,
    getAllQuizOneByLessonId,getAllQuizTwoByLessonId,getAllQuizThreeByLessonId,getGreQuizListByLesson,addQuizDiscussion,getQuizDiscussion,
    addGreQuiz,editGreQuiz,deleteGreQuiz,addExamQuiz,editExamQuiz,deleteExamQuiz,getAllExamQuizByLessonId,getAllGreQuizesByLessonId


}