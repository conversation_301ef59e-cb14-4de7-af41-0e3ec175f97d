{"name": "news_lexica_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.9.0", "axios": "^1.6.8", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "firebase-admin": "^12.0.0", "jsonwebtoken": "^9.0.2", "mysql": "^2.18.1", "uuid": "^9.0.1"}, "devDependencies": {"prisma": "^6.9.0"}}