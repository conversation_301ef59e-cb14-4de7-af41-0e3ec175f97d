const morgan = require('morgan');
const logger = require('../utils/logger');
const config = require('../config/environment');

// Custom token for response time in milliseconds
morgan.token('response-time-ms', (req, res) => {
  const responseTime = res.getHeader('X-Response-Time');
  return responseTime ? `${responseTime}ms` : '-';
});

// Custom token for user ID
morgan.token('user-id', (req) => {
  return req.user ? req.user.id || req.user : '-';
});

// Custom token for request ID (if you implement request tracking)
morgan.token('request-id', (req) => {
  return req.requestId || '-';
});

// Custom token for real IP (considering proxies)
morgan.token('real-ip', (req) => {
  return req.ip || req.connection.remoteAddress || '-';
});

// Define different log formats for different environments
const developmentFormat = ':method :url :status :response-time ms - :res[content-length] bytes';

const productionFormat = JSON.stringify({
  timestamp: ':date[iso]',
  method: ':method',
  url: ':url',
  status: ':status',
  responseTime: ':response-time-ms',
  contentLength: ':res[content-length]',
  userAgent: ':user-agent',
  ip: ':real-ip',
  userId: ':user-id',
  requestId: ':request-id',
  referrer: ':referrer',
});

// Create different loggers for different environments
const createRequestLogger = () => {
  const isDevelopment = config.nodeEnv === 'development';
  
  if (isDevelopment) {
    return morgan(developmentFormat, {
      stream: logger.stream,
    });
  } else {
    return morgan(productionFormat, {
      stream: logger.stream,
    });
  }
};

// Enhanced request logger with additional metadata
const enhancedRequestLogger = (req, res, next) => {
  const startTime = Date.now();
  
  // Generate request ID for tracking
  req.requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  // Override res.end to capture response time and log additional data
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const responseTime = Date.now() - startTime;
    
    // Set response time header
    res.setHeader('X-Response-Time', responseTime);
    
    // Log detailed request information
    logger.logRequest(req, res, responseTime);
    
    // Log slow requests (over 1 second)
    if (responseTime > 1000) {
      logger.warn('Slow Request Detected', {
        method: req.method,
        url: req.url,
        responseTime: `${responseTime}ms`,
        userId: req.user || null,
        requestId: req.requestId,
      });
    }
    
    // Call original end method
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

// Error request logger
const errorRequestLogger = (err, req, res, next) => {
  logger.logError(err, req, {
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
  });
  
  next(err);
};

// Security event logger
const securityLogger = (event, req, details = {}) => {
  logger.warn('Security Event', {
    event,
    ip: req.ip,
    userAgent: req.headers['user-agent'],
    url: req.url,
    method: req.method,
    userId: req.user || null,
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
    ...details,
  });
};

// Rate limit logger
const rateLimitLogger = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    if (res.statusCode === 429) {
      securityLogger('RATE_LIMIT_EXCEEDED', req, {
        rateLimitInfo: {
          limit: res.getHeader('X-RateLimit-Limit'),
          remaining: res.getHeader('X-RateLimit-Remaining'),
          reset: res.getHeader('X-RateLimit-Reset'),
        },
      });
    }
    
    originalSend.call(this, data);
  };
  
  next();
};

// Authentication event logger
const authEventLogger = {
  loginAttempt: (req, phone, success, reason = null) => {
    logger.logAuth('LOGIN_ATTEMPT', phone, req.ip, success, reason);
  },
  
  loginSuccess: (req, userId) => {
    logger.logAuth('LOGIN_SUCCESS', userId, req.ip, true);
  },
  
  loginFailure: (req, phone, reason) => {
    logger.logAuth('LOGIN_FAILURE', phone, req.ip, false, reason);
  },
  
  logout: (req, userId) => {
    logger.logAuth('LOGOUT', userId, req.ip, true);
  },
  
  tokenRefresh: (req, userId, success, reason = null) => {
    logger.logAuth('TOKEN_REFRESH', userId, req.ip, success, reason);
  },
  
  otpRequest: (req, phone) => {
    logger.logAuth('OTP_REQUEST', phone, req.ip, true);
  },
  
  otpVerification: (req, phone, success, reason = null) => {
    logger.logAuth('OTP_VERIFICATION', phone, req.ip, success, reason);
  },
};

module.exports = {
  requestLogger: createRequestLogger(),
  enhancedRequestLogger,
  errorRequestLogger,
  securityLogger,
  rateLimitLogger,
  authEventLogger,
};
