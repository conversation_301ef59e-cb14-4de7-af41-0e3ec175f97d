const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");
const { validateBody, validateQuery, validateParams } = require("../../middleware/validateRequest");
const Joi = require("joi");

const {
  createDiscussion,
  getDiscussionsByLessonId,
  voteOnDiscussion,
  updateDiscussion,
  deleteDiscussion,
} = require("../../controller/v2/discussions.controller");

// Validation schemas
const createDiscussionSchema = Joi.object({
  lessonId: Joi.number().integer().positive().required(),
  comment: Joi.string().required().min(1).max(1000),
  parentId: Joi.string().optional(),
});

const updateDiscussionSchema = Joi.object({
  comment: Joi.string().required().min(1).max(1000),
});

const voteSchema = Joi.object({
  discussionId: Joi.string().required(),
  voteType: Joi.string().valid('UPVOTE', 'DOWNVOTE').required(),
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().positive().default(1),
  limit: Joi.number().integer().positive().max(100).default(10),
});

const lessonIdSchema = Joi.object({
  lessonId: Joi.number().integer().positive().required(),
});

const discussionIdSchema = Joi.object({
  id: Joi.string().required(),
});

/**
 * @route   GET /api/v2/lessons/:lessonId/discussions
 * @desc    Get all discussions for a specific lesson
 * @access  Public
 * @query   ?page=1&limit=10
 */
router.get(
  "/lessons/:lessonId",
  validateParams(lessonIdSchema),
  validateQuery(paginationSchema),
  getDiscussionsByLessonId
);

/**
 * @route   POST /api/v2/discussions
 * @desc    Create a new discussion or reply
 * @access  Private (Authenticated users)
 * @body    { lessonId: number, comment: string, parentId?: string }
 */
router.post(
  "/",
  verifyJWT,
  validateBody(createDiscussionSchema),
  createDiscussion
);

/**
 * @route   PUT /api/v2/discussions/:id
 * @desc    Update a discussion comment
 * @access  Private (Owner only)
 * @body    { comment: string }
 */
router.put(
  "/:id",
  verifyJWT,
  validateParams(discussionIdSchema),
  validateBody(updateDiscussionSchema),
  updateDiscussion
);

/**
 * @route   DELETE /api/v2/discussions/:id
 * @desc    Delete a discussion
 * @access  Private (Owner only)
 */
router.delete(
  "/:id",
  verifyJWT,
  validateParams(discussionIdSchema),
  deleteDiscussion
);

/**
 * @route   POST /api/v2/discussions/vote
 * @desc    Vote on a discussion (upvote/downvote)
 * @access  Private (Authenticated users)
 * @body    { discussionId: string, voteType: 'UPVOTE' | 'DOWNVOTE' }
 */
router.post(
  "/vote",
  verifyJWT,
  validateBody(voteSchema),
  voteOnDiscussion
);

module.exports = router;
