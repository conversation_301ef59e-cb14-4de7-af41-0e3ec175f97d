const Joi = require("joi");

// Validation middleware for request body
const validateBody = (schema) => (req, res, next) => {
  const { error, value } = schema.validate(req.body, {
    abortEarly: false, // Return all validation errors
    stripUnknown: true, // Remove unknown fields
    convert: true, // Convert strings to appropriate types
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.path.join("."),
      message: detail.message,
      value: detail.context?.value,
    }));

    return res.status(400).json({
      success: false,
      message: "Validation failed",
      errors,
    });
  }

  // Replace req.body with validated and sanitized data
  req.body = value;
  next();
};

// Validation middleware for query parameters
const validateQuery = (schema) => (req, res, next) => {
  const { error, value } = schema.validate(req.query, {
    abortEarly: false,
    stripUnknown: true,
    convert: true,
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.path.join("."),
      message: detail.message,
      value: detail.context?.value,
    }));

    return res.status(400).json({
      success: false,
      message: "Query validation failed",
      errors,
    });
  }

  req.query = value;
  next();
};

// Validation middleware for URL parameters
const validateParams = (schema) => (req, res, next) => {
  const { error, value } = schema.validate(req.params, {
    abortEarly: false,
    stripUnknown: true,
    convert: true,
  });

  if (error) {
    const errors = error.details.map((detail) => ({
      field: detail.path.join("."),
      message: detail.message,
      value: detail.context?.value,
    }));

    return res.status(400).json({
      success: false,
      message: "Parameter validation failed",
      errors,
    });
  }

  req.params = value;
  next();
};

// Legacy support for existing code
const validateRequest = validateBody;

module.exports = {
  validateBody,
  validateQuery,
  validateParams,
  validateRequest, // For backward compatibility
};
