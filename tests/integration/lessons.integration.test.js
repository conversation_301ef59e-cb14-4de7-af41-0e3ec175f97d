const request = require('supertest');
const app = require('../../app');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

describe('Lessons Integration Tests', () => {
  let adminUser;
  let adminToken;
  let testLesson;

  beforeAll(async () => {
    // Create admin user
    const hashedPassword = await bcrypt.hash('adminpassword123', 12);
    adminUser = await prisma.user.create({
      data: {
        name: 'Admin User',
        phone: '01712345679',
        password: hashedPassword,
        role: {
          create: {
            role: 'ADMIN',
          },
        },
      },
    });

    // Generate admin token
    adminToken = global.testUtils.generateAdminToken(adminUser.id);
  });

  afterAll(async () => {
    // Clean up test data
    if (testLesson) {
      await prisma.quizOne.deleteMany({ where: { lessonId: testLesson.id } });
      await prisma.quizTwo.deleteMany({ where: { lessonId: testLesson.id } });
      await prisma.quizThree.deleteMany({ where: { lessonId: testLesson.id } });
      await prisma.lesson.delete({ where: { id: testLesson.id } });
    }
    
    await prisma.userRole.deleteMany({ where: { userId: adminUser.id } });
    await prisma.user.delete({ where: { id: adminUser.id } });
    await prisma.$disconnect();
  });

  describe('GET /api/v2/lessons', () => {
    it('should get lessons with pagination', async () => {
      const response = await request(app)
        .get('/api/v2/lessons')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toMatchObject({
        page: 1,
        limit: 10,
        total: expect.any(Number),
        totalPages: expect.any(Number),
      });
    });

    it('should filter lessons by category', async () => {
      const response = await request(app)
        .get('/api/v2/lessons')
        .query({ category: 'news' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toBeInstanceOf(Array);
    });

    it('should validate query parameters', async () => {
      const response = await request(app)
        .get('/api/v2/lessons')
        .query({ page: 'invalid', limit: 'invalid' });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('POST /api/v2/lessons', () => {
    it('should create lesson with admin token', async () => {
      const lessonData = global.testUtils.createTestLesson();

      const response = await request(app)
        .post('/api/v2/lessons')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(lessonData);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        lessonTitle: lessonData.lessonTitle,
        title: lessonData.title,
        lessonNumber: lessonData.lessonNumber,
      });

      testLesson = response.body.data;
    });

    it('should reject creation without admin token', async () => {
      const lessonData = global.testUtils.createTestLesson();

      const response = await request(app)
        .post('/api/v2/lessons')
        .send(lessonData);

      expect(response.status).toBe(401);
    });

    it('should validate lesson data', async () => {
      const invalidLessonData = {
        lessonTitle: '', // Invalid: empty title
        // Missing required fields
      };

      const response = await request(app)
        .post('/api/v2/lessons')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidLessonData);

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('GET /api/v2/lessons/:id', () => {
    it('should get lesson by ID', async () => {
      if (!testLesson) {
        // Create a test lesson if not already created
        const lessonData = global.testUtils.createTestLesson();
        const createResponse = await request(app)
          .post('/api/v2/lessons')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(lessonData);
        testLesson = createResponse.body.data;
      }

      const response = await request(app)
        .get(`/api/v2/lessons/${testLesson.id}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toMatchObject({
        id: testLesson.id,
        lessonTitle: testLesson.lessonTitle,
      });
    });

    it('should return 404 for non-existent lesson', async () => {
      const response = await request(app)
        .get('/api/v2/lessons/99999');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Lesson not found');
    });

    it('should validate lesson ID parameter', async () => {
      const response = await request(app)
        .get('/api/v2/lessons/invalid-id');

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('PUT /api/v2/lessons/:id', () => {
    it('should update lesson with admin token', async () => {
      if (!testLesson) {
        const lessonData = global.testUtils.createTestLesson();
        const createResponse = await request(app)
          .post('/api/v2/lessons')
          .set('Authorization', `Bearer ${adminToken}`)
          .send(lessonData);
        testLesson = createResponse.body.data;
      }

      const updateData = {
        lessonTitle: 'Updated Test Lesson',
        title: 'Updated Article Title',
      };

      const response = await request(app)
        .put(`/api/v2/lessons/${testLesson.id}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateData);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data.lessonTitle).toBe('Updated Test Lesson');
      expect(response.body.data.title).toBe('Updated Article Title');
    });

    it('should reject update without admin token', async () => {
      const response = await request(app)
        .put(`/api/v2/lessons/${testLesson.id}`)
        .send({ title: 'Unauthorized Update' });

      expect(response.status).toBe(401);
    });

    it('should return 404 for non-existent lesson', async () => {
      const response = await request(app)
        .put('/api/v2/lessons/99999')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({ title: 'Update Non-existent' });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Lesson not found');
    });
  });

  describe('DELETE /api/v2/lessons/:id', () => {
    it('should delete lesson with admin token', async () => {
      // Create a lesson specifically for deletion test
      const lessonData = global.testUtils.createTestLesson();
      const createResponse = await request(app)
        .post('/api/v2/lessons')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(lessonData);
      const lessonToDelete = createResponse.body.data;

      const response = await request(app)
        .delete(`/api/v2/lessons/${lessonToDelete.id}`)
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Lesson deleted successfully');

      // Verify lesson is actually deleted
      const getResponse = await request(app)
        .get(`/api/v2/lessons/${lessonToDelete.id}`);
      expect(getResponse.status).toBe(404);
    });

    it('should reject deletion without admin token', async () => {
      const response = await request(app)
        .delete(`/api/v2/lessons/${testLesson.id}`);

      expect(response.status).toBe(401);
    });

    it('should return 404 for non-existent lesson', async () => {
      const response = await request(app)
        .delete('/api/v2/lessons/99999')
        .set('Authorization', `Bearer ${adminToken}`);

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Lesson not found');
    });
  });
});
