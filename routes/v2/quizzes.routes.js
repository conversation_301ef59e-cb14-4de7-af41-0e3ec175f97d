const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");
const { verifyRoles } = require("../../middleware/verifyRoles");
const { validateBody, validateQuery, validateParams } = require("../../middleware/validateRequest");
const Joi = require("joi");

const {
  createQuizOne,
  updateQuizOne,
  deleteQuizOne,
  createQuizTwo,
  updateQuizTwo,
  deleteQuizTwo,
  createQuizThree,
  updateQuizThree,
  deleteQuizThree,
  getQuizzesByLessonId,
} = require("../../controller/v2/quizzes.controller");

// Validation schemas
const createQuizOneSchema = Joi.object({
  question: Joi.string().required().min(1),
  option1: Joi.string().required().min(1),
  option2: Joi.string().required().min(1),
  option3: Joi.string().required().min(1),
  option4: Joi.string().required().min(1),
  answer: Joi.string().required().min(1),
  lessonId: Joi.number().integer().positive().required(),
});

const updateQuizOneSchema = Joi.object({
  question: Joi.string().min(1),
  option1: Joi.string().min(1),
  option2: Joi.string().min(1),
  option3: Joi.string().min(1),
  option4: Joi.string().min(1),
  answer: Joi.string().min(1),
});

const createQuizTwoSchema = Joi.object({
  question: Joi.string().required().min(1),
  answer: Joi.string().required().min(1),
  lessonId: Joi.number().integer().positive().required(),
});

const updateQuizTwoSchema = Joi.object({
  question: Joi.string().min(1),
  answer: Joi.string().min(1),
});

const createQuizThreeSchema = Joi.object({
  question: Joi.string().required().min(1),
  option1: Joi.string().required().min(1),
  option2: Joi.string().required().min(1),
  option3: Joi.string().required().min(1),
  option4: Joi.string().required().min(1),
  answer: Joi.string().required().min(1),
  lessonId: Joi.number().integer().positive().required(),
});

const updateQuizThreeSchema = Joi.object({
  question: Joi.string().min(1),
  option1: Joi.string().min(1),
  option2: Joi.string().min(1),
  option3: Joi.string().min(1),
  option4: Joi.string().min(1),
  answer: Joi.string().min(1),
});

const quizIdSchema = Joi.object({
  id: Joi.string().required(),
});

const lessonIdSchema = Joi.object({
  lessonId: Joi.number().integer().positive().required(),
});

const quizTypeSchema = Joi.object({
  type: Joi.string().valid('quizOne', 'quizTwo', 'quizThree').default('quizOne'),
});

// Quiz Type One Routes
/**
 * @route   POST /api/v2/quizzes/type-one
 * @desc    Create a new quiz type one
 * @access  Private (Admin only)
 */
router.post(
  "/type-one",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateBody(createQuizOneSchema),
  createQuizOne
);

/**
 * @route   PUT /api/v2/quizzes/type-one/:id
 * @desc    Update a quiz type one
 * @access  Private (Admin only)
 */
router.put(
  "/type-one/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(quizIdSchema),
  validateBody(updateQuizOneSchema),
  updateQuizOne
);

/**
 * @route   DELETE /api/v2/quizzes/type-one/:id
 * @desc    Delete a quiz type one
 * @access  Private (Admin only)
 */
router.delete(
  "/type-one/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(quizIdSchema),
  deleteQuizOne
);

// Quiz Type Two Routes
/**
 * @route   POST /api/v2/quizzes/type-two
 * @desc    Create a new quiz type two
 * @access  Private (Admin only)
 */
router.post(
  "/type-two",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateBody(createQuizTwoSchema),
  createQuizTwo
);

/**
 * @route   PUT /api/v2/quizzes/type-two/:id
 * @desc    Update a quiz type two
 * @access  Private (Admin only)
 */
router.put(
  "/type-two/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(quizIdSchema),
  validateBody(updateQuizTwoSchema),
  updateQuizTwo
);

/**
 * @route   DELETE /api/v2/quizzes/type-two/:id
 * @desc    Delete a quiz type two
 * @access  Private (Admin only)
 */
router.delete(
  "/type-two/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(quizIdSchema),
  deleteQuizTwo
);

// Quiz Type Three Routes
/**
 * @route   POST /api/v2/quizzes/type-three
 * @desc    Create a new quiz type three
 * @access  Private (Admin only)
 */
router.post(
  "/type-three",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateBody(createQuizThreeSchema),
  createQuizThree
);

/**
 * @route   PUT /api/v2/quizzes/type-three/:id
 * @desc    Update a quiz type three
 * @access  Private (Admin only)
 */
router.put(
  "/type-three/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(quizIdSchema),
  validateBody(updateQuizThreeSchema),
  updateQuizThree
);

/**
 * @route   DELETE /api/v2/quizzes/type-three/:id
 * @desc    Delete a quiz type three
 * @access  Private (Admin only)
 */
router.delete(
  "/type-three/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(quizIdSchema),
  deleteQuizThree
);

// Get quizzes by lesson
/**
 * @route   GET /api/v2/quizzes/lessons/:lessonId
 * @desc    Get all quizzes for a specific lesson
 * @access  Public
 * @query   ?type=quizOne|quizTwo|quizThree
 */
router.get(
  "/lessons/:lessonId",
  validateParams(lessonIdSchema),
  validateQuery(quizTypeSchema),
  getQuizzesByLessonId
);

module.exports = router;
