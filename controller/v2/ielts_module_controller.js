const prisma = require("../../prisma/index");
const { getAudioPath, removeAudio } = require("../../utils/audioUploader");

const getBooksList = async (req, res) => {
    try {
        const books = await prisma.ietsBook.findMany(); // Correct model name
        res.status(200).json({booksList:books}); // Fixed typo in "json"
    } catch (err) {
        console.error("Error fetching books:", err);
        res.status(500).json({ message: "Failed to retrieve books." });
    }
};

const addSingleBook = async (req, res) => {
    const { name, subtitle } = req.body;
    try {
        const newBook = await prisma.ietsBook.create({ // Correct model name
            data: {
                name,
                subtitle,
            },
        });
        res.status(201).json(newBook);
    } catch (err) {
        console.error("Error adding book:", err);
        res.status(500).json({ message: "Failed to add the book." });
    }
};


const getSingleBook = async (req, res) => {
    const { id } = req.params;
    try {
        const book = await prisma.ietsBook.findUnique({
            where: { id: parseInt(id) },
        });
        if (!book) {
            return res.status(404).json({ message: "Book not found." });
        }
        res.status(200).json(book);
    } catch (err) {
        console.error("Error fetching book:", err);
        res.status(500).json({ message: "Failed to retrieve the book." });
    }
};

const updateBook = async (req, res) => {
    const { id } = req.params;
    const { name, subtitle } = req.body;
    try {
        const updatedBook = await prisma.ietsBook.update({
            where: { id: parseInt(id) },
            data: { name, subtitle },
        });
        res.status(200).json(updatedBook);
    } catch (err) {
        console.error("Error updating book:", err);
        res.status(500).json({ message: "Failed to update the book." });
    }
};

const deleteBook = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.ietsBook.delete({
            where: { id: parseInt(id) },
        });
        res.status(200).json({ message: "Book deleted successfully." });
    } catch (err) {
        console.error("Error deleting book:", err);
        res.status(500).json({ message: "Failed to delete the book." });
    }
};


const getReadingLists = async (req, res) => {
    const { id } = req.query;
    if (!id) {
        return res.status(400).json({ message: "Book ID is required." });
    }
    
    try {
        const readingLists = await prisma.ieltsReadingList.findMany({
            where: { ieltsBookId: parseInt(id) }
        }); // Correct model name
        res.status(200).json({readingList:readingLists}); // Fixed typo in "json"
    } catch (err) {
        console.error("Error fetching reading lists:", err);
        res.status(500).json({ message: "Failed to retrieve reading lists." });
    }
};


const addReadingList = async (req, res) => {
    const { name, ieltsBookId } = req.body;
    if(!name || !ieltsBookId) {
        return res.status(400).json({ message: "Name and book ID are required." });
    }
    try {
        const newReadingList = await prisma.ieltsReadingList.create({
            data: {
                name,
                ieltsBookId, // Foreign key to associate with IeltsBook
            },
        });
        res.status(201).json(newReadingList);
    } catch (err) {
        console.error("Error adding reading list:", err);
        res.status(500).json({ message: "Failed to add the reading list." });
    }
};


const getSingleReadingList = async (req, res) => {
    const { id } = req.params;
    try {
        const readingList = await prisma.ieltsReadingList.findUnique({
            where: { id: parseInt(id) },
            include: {
                ieltsBook: true, // Include associated book
                IeltsReadingPassage: true,

            },
        });
        if (!readingList) {
            return res.status(404).json({ message: "Reading list not found." });
        }
        res.status(200).json(readingList);
    } catch (err) {
        console.error("Error fetching reading list:", err);
        res.status(500).json({ message: "Failed to retrieve the reading list." });
    }
};



const updateReadingList = async (req, res) => {
    const { id } = req.params;
    const { name } = req.body;
    try {
        const updatedReadingList = await prisma.ieltsReadingList.update({
            where: { id: parseInt(id) },
            data: {
                name
            },
        });
        res.status(200).json(updatedReadingList);
    } catch (err) {
        console.error("Error updating reading list:", err);
        res.status(500).json({ message: "Failed to update the reading list." });
    }
};


const deleteReadingList = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.ieltsReadingList.delete({
            where: { id: parseInt(id) },
        });
        res.status(200).json({ message: "Reading list deleted successfully." });
    } catch (err) {
        console.error("Error deleting reading list:", err);
        res.status(500).json({ message: "Failed to delete the reading list." });
    }
};


const getReadingPassages = async (req, res) => {
    const { id } = req.query;
    try {
        const passages = await prisma.ieltsReadingPassage.findMany({
            where: { ieltsRadingListId: parseInt(id) },
        });
        res.status(200).json({passageList:passages});
    } catch (err) {
        console.error("Error fetching reading passages:", err);
        res.status(500).json({ message: "Failed to retrieve reading passages." });
    }
};


const addReadingPassage = async (req, res) => {
    const { name, ieltsRadingListId } = req.body;
    try {
        const newPassage = await prisma.ieltsReadingPassage.create({
            data: {
                name,
                ieltsRadingListId, // Foreign key to associate with IeltsReadingList
            },
        });
        res.status(201).json(newPassage);
    } catch (err) {
        console.error("Error adding reading passage:", err);
        res.status(500).json({ message: "Failed to add the reading passage." });
    }
};




const getSingleReadingPassage = async (req, res) => {
    const { id } = req.params;
    try {
        const passage = await prisma.ieltsReadingPassage.findUnique({
            where: { id: parseInt(id) },
            include: {
                IeltsReadingPassageQuizOne: true, // Include related quizzes (QuizOne)
                IeltsReadingPassageQuizTwo: true, // Include related quizzes (QuizTwo)
                Passage: true,
                IeltsReadingPassageExample:true
            },
        });
        if (!passage) {
            return res.status(404).json({ message: "Reading passage not found." });
        }
        res.status(200).json(passage);
    } catch (err) {
        console.error("Error fetching reading passage:", err);
        res.status(500).json({ message: "Failed to retrieve the reading passage." });
    }
};



const updateReadingPassage = async (req, res) => {
    const { id } = req.params;
    const { name} = req.body;
    try {
        const updatedPassage = await prisma.ieltsReadingPassage.update({
            where: { id: parseInt(id) },
            data: {
                name,
            },
        });
        res.status(200).json(updatedPassage);
    } catch (err) {
        console.error("Error updating reading passage:", err);
        res.status(500).json({ message: "Failed to update the reading passage." });
    }
};



const deleteReadingPassage = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.ieltsReadingPassage.delete({
            where: { id: parseInt(id) },
        });
        res.status(200).json({ message: "Reading passage deleted successfully." });
    } catch (err) {
        console.error("Error deleting reading passage:", err);
        res.status(500).json({ message: "Failed to delete the reading passage." });
    }
};


const getQuizzesOne = async (req, res) => {
    try {
        const quizzes = await prisma.ieltsReadingPassageQuizOne.findMany({
            include: {
                ieltsReadingPassage: true, // Include associated reading passage
            },
        });
        res.status(200).json(quizzes);
    } catch (err) {
        console.error("Error fetching quizzes:", err);
        res.status(500).json({ message: "Failed to retrieve quizzes." });
    }
};

const addQuizOne = async (req, res) => {
    const { question, option1, answer } = req.body;
    const { id } = req.params; // Assuming you want to use the ID from the URL  
    try {
        const newQuiz = await prisma.ieltsReadingPassageQuizOne.create({
            data: {
                question,
                option1,
                answer,
                ieltsReadingPassageId:parseInt(id), // Foreign key to associate with a reading passage
            },
        });
        res.status(201).json(newQuiz);
    } catch (err) {
        console.error("Error adding quiz:", err);
        res.status(500).json({ message: "Failed to add the quiz." });
    }
};





const updateQuizOne = async (req, res) => {
    const { id } = req.params;
    const { question, option1, answer, ieltsReadingPassageId } = req.body;
    try {
        const updatedQuiz = await prisma.ieltsReadingPassageQuizOne.update({
            where: { id: parseInt(id) },
            data: {
                question,
                option1,
                answer,
                ieltsReadingPassageId,
            },
        });
        res.status(200).json(updatedQuiz);
    } catch (err) {
        console.error("Error updating quiz:", err);
        res.status(500).json({ message: "Failed to update the quiz." });
    }
};

const deleteQuizOne = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.ieltsReadingPassageQuizOne.delete({
            where: { id: parseInt(id) },
        });
        res.status(200).json({ message: "Quiz deleted successfully." });
    } catch (err) {
        console.error("Error deleting quiz:", err);
        res.status(500).json({ message: "Failed to delete the quiz." });
    }
};



const getQuizzesTwo = async (req, res) => {
    try {
        const quizzes = await prisma.ieltsReadingPassageQuizTwo.findMany({
            include: {
                ieltsReadingPassage: true, // Include associated reading passage
            },
        });
        res.status(200).json(quizzes);
    } catch (err) {
        console.error("Error fetching quizzes:", err);
        res.status(500).json({ message: "Failed to retrieve quizzes." });
    }
};

const addQuizTwo = async (req, res) => {
    const { question, option1, answer, } = req.body;
    const { id } = req.params; // Assuming you want to use the ID from the URL
    try {
        const newQuiz = await prisma.ieltsReadingPassageQuizTwo.create({
            data: {
                question,
                option1,
                answer,
                ieltsReadingPassageId:parseInt(id), // Foreign key to associate with a reading passage
            },
        });
        res.status(201).json(newQuiz);
    } catch (err) {
        console.error("Error adding quiz:", err);
        res.status(500).json({ message: "Failed to add the quiz." });
    }
};

const getSingleQuizTwo = async (req, res) => {
    const { id } = req.params;
    try {
        const quiz = await prisma.ieltsReadingPassageQuizTwo.findUnique({
            where: { id: parseInt(id) },
            include: {
                ieltsReadingPassage: true, // Include associated reading passage
            },
        });
        if (!quiz) {
            return res.status(404).json({ message: "Quiz not found." });
        }
        res.status(200).json(quiz);
    } catch (err) {
        console.error("Error fetching quiz:", err);
        res.status(500).json({ message: "Failed to retrieve the quiz." });
    }
};

const updateQuizTwo = async (req, res) => {
    const { id } = req.params;
    const { question, option1, answer, ieltsReadingPassageId } = req.body;
    try {
        const updatedQuiz = await prisma.ieltsReadingPassageQuizTwo.update({
            where: { id: parseInt(id) },
            data: {
                question,
                option1,
                answer,
                ieltsReadingPassageId,
            },
        });
        res.status(200).json(updatedQuiz);
    } catch (err) {
        console.error("Error updating quiz:", err);
        res.status(500).json({ message: "Failed to update the quiz." });
    }
};




const deleteQuizTwo = async (req, res) => {
    const { id } = req.params;
    try {
        await prisma.ieltsReadingPassageQuizTwo.delete({
            where: { id: parseInt(id) },
        });
        res.status(200).json({ message: "Quiz deleted successfully." });
    } catch (err) {
        console.error("Error deleting quiz:", err);
        res.status(500).json({ message: "Failed to delete the quiz." });
    }
};

const addPassage = async (req, res) => {
    const { title, paragraph, ieltsReadingPassageId } = req.body;
    const audioPath = getAudioPath(req);
    
    try {
        const newPassage = await prisma.passage.create({
            data: {
                title,
                paragraph,
                audio: audioPath,
                ieltsReadingPassageId, // No need to parse as integer anymore
            },
        });
        res.status(201).json(newPassage);
    } catch (err) {
        if (audioPath) {
            removeAudio(audioPath);
        }
        console.error("Error adding passage:", err);
        res.status(500).json({ message: "Failed to add the passage." });
    }
};

const updatePassage = async (req, res) => {
    const { id } = req.params;
    const { title, paragraph, ieltsReadingPassageId } = req.body;
    const audioPath = getAudioPath(req);
    
    try {
        let oldAudioPath = null;
        if (audioPath) {
            const existingPassage = await prisma.passage.findUnique({
                where: { id }, // No need to parse as integer anymore
                select: { audio: true }
            });
            oldAudioPath = existingPassage?.audio;
        }
        
        const updatedPassage = await prisma.passage.update({
            where: { id }, // No need to parse as integer anymore
            data: {
                title,
                paragraph,
                ...(audioPath && { audio: audioPath }),
                ieltsReadingPassageId,
            },
        });
        
        if (audioPath && oldAudioPath) {
            removeAudio(oldAudioPath);
        }

        res.status(200).json(updatedPassage);
    } catch (err) {
        if (audioPath) {
            removeAudio(audioPath);
        }
        console.error("Error updating passage:", err);
        res.status(500).json({ message: "Failed to update the passage." });
    }
};

const deletePassage = async (req, res) => {
    const { id } = req.params;
    
    try {
        const passage = await prisma.passage.findUnique({
            where: { id }, // No need to parse as integer anymore
            select: { audio: true }
        });
        
        await prisma.passage.delete({
            where: { id }, // No need to parse as integer anymore
        });
        
        if (passage?.audio) {
            removeAudio(passage.audio);
        }
        
        res.status(200).json({ message: "Passage deleted successfully." });
    } catch (err) {
        console.error("Error deleting passage:", err);
        res.status(500).json({ message: "Failed to delete the passage." });
    }
};

const addQuizesExmple = async (req, res) => {
    const { word, synonym, antonym, ieltsReadingPassageId } = req.body;
    try {
        const newQuiz = await prisma.ieltsReadingPassageExample.create({
            data: {
                word,
                synonym,
                antonym,
                ieltsReadingPassageId, // Foreign key to associate with a reading passage
            },
        });
        res.status(201).json(newQuiz);
    } catch (err) {
        console.error("Error adding quiz:", err);
        res.status(500).json({ message: "Failed to add the quiz." });
    }
};

const updateQuizExample = async (req, res) => {
    const { id } = req.params;
    const { word, synonym, antonym, ieltsReadingPassageId } = req.body;

    try {
        const updatedQuiz = await prisma.ieltsReadingPassageExample.update({
            where: { id: Number(id) }, // Assuming `id` is a number
            data: {
                word,
                synonym,
                antonym,
                ieltsReadingPassageId, // Updating foreign key if needed
            },
        });

        res.status(200).json(updatedQuiz);
    } catch (err) {
        console.error("Error updating quiz:", err);
        res.status(500).json({ message: "Failed to update the quiz." });
    }
};

const deleteQuizExample = async (req, res) => {
    const { id } = req.params;

    try {
        await prisma.ieltsReadingPassageExample.delete({
            where: { id: Number(id) }, // Assuming `id` is a number
        });

        res.status(200).json({ message: "Quiz example deleted successfully." });
    } catch (err) {
        console.error("Error deleting quiz:", err);
        res.status(500).json({ message: "Failed to delete the quiz." });
    }
};

module.exports = {getBooksList,addSingleBook,getSingleBook,updateBook,deleteBook,getReadingLists,addReadingList,getSingleReadingList,updateReadingList,
    deleteReadingList,getReadingPassages,addReadingPassage,getSingleReadingPassage,updateReadingPassage,deleteReadingPassage,addQuizOne,getQuizzesOne,updateQuizOne,
    deleteQuizOne,getQuizzesTwo,addQuizTwo,getSingleQuizTwo,updateQuizTwo,deleteQuizTwo,addPassage,deletePassage,addQuizesExmple,updateQuizExample,updatePassage,deleteQuizExample};
