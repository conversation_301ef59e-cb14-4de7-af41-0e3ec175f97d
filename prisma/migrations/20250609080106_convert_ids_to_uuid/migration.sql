/*
  Warnings:

  - The primary key for the `ieltsreadinglist` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ieltsreadingpassage` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ieltsreadingpassageexample` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ieltsreadingpassagequizone` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ieltsreadingpassagequiztwo` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `ietsbook` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `lesson` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `lessondiscussion` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `passage` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `promotion` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `quizone` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `quizthree` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `quiztwo` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `scoreboard` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `subscriptionrequest` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `user` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `userrole` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The primary key for the `vote` table will be changed. If it partially fails, the table could be left without primary key constraint.

*/
-- DropForeignKey
ALTER TABLE `ieltsreadinglist` DROP FOREIGN KEY `IeltsReadingList_ieltsBookId_fkey`;

-- DropForeignKey
ALTER TABLE `ieltsreadingpassage` DROP FOREIGN KEY `IeltsReadingPassage_ieltsRadingListId_fkey`;

-- DropForeignKey
ALTER TABLE `ieltsreadingpassageexample` DROP FOREIGN KEY `IeltsReadingPassageExample_ieltsReadingPassageId_fkey`;

-- DropForeignKey
ALTER TABLE `ieltsreadingpassagequizone` DROP FOREIGN KEY `IeltsReadingPassageQuizOne_ieltsReadingPassageId_fkey`;

-- DropForeignKey
ALTER TABLE `ieltsreadingpassagequiztwo` DROP FOREIGN KEY `IeltsReadingPassageQuizTwo_ieltsReadingPassageId_fkey`;

-- DropForeignKey
ALTER TABLE `lessondiscussion` DROP FOREIGN KEY `LessonDiscussion_lessonId_fkey`;

-- DropForeignKey
ALTER TABLE `lessondiscussion` DROP FOREIGN KEY `LessonDiscussion_parentId_fkey`;

-- DropForeignKey
ALTER TABLE `lessondiscussion` DROP FOREIGN KEY `LessonDiscussion_userId_fkey`;

-- DropForeignKey
ALTER TABLE `passage` DROP FOREIGN KEY `Passage_ieltsReadingPassageId_fkey`;

-- DropForeignKey
ALTER TABLE `scoreboard` DROP FOREIGN KEY `ScoreBoard_userId_fkey`;

-- DropForeignKey
ALTER TABLE `subscriptionrequest` DROP FOREIGN KEY `SubscriptionRequest_userId_fkey`;

-- DropForeignKey
ALTER TABLE `userrole` DROP FOREIGN KEY `UserRole_userId_fkey`;

-- DropForeignKey
ALTER TABLE `vote` DROP FOREIGN KEY `Vote_discussionId_fkey`;

-- DropForeignKey
ALTER TABLE `vote` DROP FOREIGN KEY `Vote_userId_fkey`;

-- DropIndex
DROP INDEX `IeltsReadingList_ieltsBookId_fkey` ON `ieltsreadinglist`;

-- DropIndex
DROP INDEX `IeltsReadingPassage_ieltsRadingListId_fkey` ON `ieltsreadingpassage`;

-- DropIndex
DROP INDEX `IeltsReadingPassageExample_ieltsReadingPassageId_fkey` ON `ieltsreadingpassageexample`;

-- DropIndex
DROP INDEX `IeltsReadingPassageQuizOne_ieltsReadingPassageId_fkey` ON `ieltsreadingpassagequizone`;

-- DropIndex
DROP INDEX `IeltsReadingPassageQuizTwo_ieltsReadingPassageId_fkey` ON `ieltsreadingpassagequiztwo`;

-- DropIndex
DROP INDEX `LessonDiscussion_lessonId_fkey` ON `lessondiscussion`;

-- DropIndex
DROP INDEX `LessonDiscussion_parentId_fkey` ON `lessondiscussion`;

-- DropIndex
DROP INDEX `LessonDiscussion_userId_fkey` ON `lessondiscussion`;

-- DropIndex
DROP INDEX `UserRole_userId_fkey` ON `userrole`;

-- DropIndex
DROP INDEX `Vote_discussionId_fkey` ON `vote`;

-- AlterTable
ALTER TABLE `ieltsreadinglist` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `ieltsBookId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `ieltsreadingpassage` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `ieltsRadingListId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `ieltsreadingpassageexample` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `ieltsReadingPassageId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `ieltsreadingpassagequizone` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `ieltsReadingPassageId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `ieltsreadingpassagequiztwo` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `ieltsReadingPassageId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `ietsbook` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `lesson` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `lessondiscussion` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `lessonId` VARCHAR(191) NOT NULL,
    MODIFY `userId` VARCHAR(191) NOT NULL,
    MODIFY `parentId` VARCHAR(191) NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `passage` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `ieltsReadingPassageId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `promotion` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `quizone` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `quizthree` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `quiztwo` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `scoreboard` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `userId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `subscriptionrequest` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `userId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `user` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `userrole` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `userId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AlterTable
ALTER TABLE `vote` DROP PRIMARY KEY,
    MODIFY `id` VARCHAR(191) NOT NULL,
    MODIFY `userId` VARCHAR(191) NOT NULL,
    MODIFY `discussionId` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- AddForeignKey
ALTER TABLE `UserRole` ADD CONSTRAINT `UserRole_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SubscriptionRequest` ADD CONSTRAINT `SubscriptionRequest_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LessonDiscussion` ADD CONSTRAINT `LessonDiscussion_lessonId_fkey` FOREIGN KEY (`lessonId`) REFERENCES `Lesson`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LessonDiscussion` ADD CONSTRAINT `LessonDiscussion_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `LessonDiscussion` ADD CONSTRAINT `LessonDiscussion_parentId_fkey` FOREIGN KEY (`parentId`) REFERENCES `LessonDiscussion`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Vote` ADD CONSTRAINT `Vote_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Vote` ADD CONSTRAINT `Vote_discussionId_fkey` FOREIGN KEY (`discussionId`) REFERENCES `LessonDiscussion`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ScoreBoard` ADD CONSTRAINT `ScoreBoard_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `IeltsReadingList` ADD CONSTRAINT `IeltsReadingList_ieltsBookId_fkey` FOREIGN KEY (`ieltsBookId`) REFERENCES `IetsBook`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `IeltsReadingPassage` ADD CONSTRAINT `IeltsReadingPassage_ieltsRadingListId_fkey` FOREIGN KEY (`ieltsRadingListId`) REFERENCES `IeltsReadingList`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `IeltsReadingPassageExample` ADD CONSTRAINT `IeltsReadingPassageExample_ieltsReadingPassageId_fkey` FOREIGN KEY (`ieltsReadingPassageId`) REFERENCES `IeltsReadingPassage`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `IeltsReadingPassageQuizOne` ADD CONSTRAINT `IeltsReadingPassageQuizOne_ieltsReadingPassageId_fkey` FOREIGN KEY (`ieltsReadingPassageId`) REFERENCES `IeltsReadingPassage`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `IeltsReadingPassageQuizTwo` ADD CONSTRAINT `IeltsReadingPassageQuizTwo_ieltsReadingPassageId_fkey` FOREIGN KEY (`ieltsReadingPassageId`) REFERENCES `IeltsReadingPassage`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `Passage` ADD CONSTRAINT `Passage_ieltsReadingPassageId_fkey` FOREIGN KEY (`ieltsReadingPassageId`) REFERENCES `IeltsReadingPassage`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
