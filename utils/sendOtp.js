const axios = require('axios');
sendSms = async (apiKey, senderId, phoneNumbers, message) => {
  const apiUrl = 'http://bulksmsbd.net/api/smsapi';
  const params = new URLSearchParams({
    api_key: apiKey,
    number: phoneNumbers,
    senderid: senderId,
    message: message
  });

  try {
    const response = await axios.post(`${apiUrl}?${params.toString()}`);
    return response.data;
  } catch (error) {
    throw new Error('Error sending SMS: ' + error.message);
  }
}

module.exports = { sendSms };