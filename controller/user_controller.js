const prisma = require("../prisma/index");

const getUserInfo = async (req, res) => {
    const { userId } = req.query;
    const userIdInt = parseInt(userId);
    if (!userId) {
        return res
            .status(400)
            .json({ message: "plase provide valid credentials" });
    }

    try {
        const user = await prisma.user.findUnique({
            where: {
                id: userIdInt
            },
            include: {
                SubscriptionRequest: true
            }
        });

        return res
            .status(200)
            .json({ message: user });

    }
    catch (err) {
        return res
            .status(400)
            .json({ message: err });
    }
}



const updateOrCreateScore = async (req, res) => {
    const { userId, score } = req.body;

    if (!userId || !score || isNaN(parseInt(score))) {
        return res.status(400).json({ message: "Valid userId and score must be provided" });
    }

    const parsedUserId = parseInt(userId);
    const parsedScore = parseInt(score);

    try {
        const existingScoreBoard = await prisma.scoreBoard.findUnique({
            where: { userId: parsedUserId }
        });

        let updatedScoreBoard;

        if (existingScoreBoard) {
            // Update the existing score
            updatedScoreBoard = await prisma.scoreBoard.update({
                where: { userId: parsedUserId },
                data: { score: existingScoreBoard.score + parsedScore }
            });
        } else {
            // Create a new score board entry
            updatedScoreBoard = await prisma.scoreBoard.create({
                data: {
                    userId: parsedUserId,
                    score: parsedScore
                }
            });
        }

        return res.status(200).json({ message: updatedScoreBoard });
    } catch (err) {
        console.error("Error updating or creating score:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};


const getUserScore = async (req, res) => {
    const { userId } = req.query;

    if (!userId || isNaN(parseInt(userId))) {
        return res.status(400).json({ message: "Valid userId must be provided" });
    }

    const parsedUserId = parseInt(userId);

    try {
        const scoreBoard = await prisma.scoreBoard.findUnique({
            where: { userId: parsedUserId }
        });

        if (!scoreBoard) {
            return res.status(404).json({ message: "User score not found" });
        }

        return res.status(200).json({ userId: parsedUserId, score: scoreBoard.score });
    } catch (err) {
        console.error("Error fetching user score:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};




const getAllScoresWithUsers = async (req, res) => {
    const { page = 1, pageSize = 10 } = req.query;

    const parsedPage = parseInt(page);
    const parsedPageSize = parseInt(pageSize);

    if (isNaN(parsedPage) || isNaN(parsedPageSize) || parsedPage <= 0 || parsedPageSize <= 0) {
        return res.status(400).json({ message: "Invalid page or pageSize parameters" });
    }

    try {
        const totalItems = await prisma.scoreBoard.count();

        const scores = await prisma.scoreBoard.findMany({
            skip: (parsedPage - 1) * parsedPageSize,
            take: parsedPageSize,
            orderBy: {
                score: 'desc'
            },
            include: {
                user: true // This will include user details with each score record
            }
        });

        return res.status(200).json({
            scores,
            totalItems,
            totalPages: Math.ceil(totalItems / parsedPageSize),
            currentPage: parsedPage
        });
    } catch (err) {
        console.error("Error fetching scores with users:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};






const getSubscriptionRequests = async (req, res) => {
    try {
        const subscriptions = await prisma.subscriptionRequest.findMany({
         
        });

        return res.status(200).json({ message: subscriptions });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};



const getAllUser = async (req, res) => {
    try {
        const { limit = 10, page = 1, q = '' } = req.query;

        const users = await prisma.user.findMany({
            where: {
                phone: {
                    contains: q,
                },
            },
            take: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit),
        });

        const totalUsers = await prisma.user.count({
            where: {
                phone: {
                    contains: q,
                },
            },
        });

        return res.status(200).json({ 
            message: 'Users retrieved successfully',
            data: users,
            total: totalUsers,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};




const addSubscription = async (req, res) => {
    const { subscrioberPhone, id, amount, duration, subscribed } = req.body;

    if (!subscrioberPhone || !id || !amount || !duration || typeof subscribed === 'undefined') {
        return res
            .status(400)
            .json({ message: "Please provide valid credentials" });
    }

    try {
        
        const userId=parseInt(id)
        const subscription = await prisma.subscriptionRequest.create({
            data: {
                subscrioberPhone,
                userId,
                amount,
                duration,
                subscribed
            }
        });

        return res.status(201).json({ message: subscription });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};



const deleteSubscription = async (req, res) => {
    const { subscriptionId, userId } = req.body;

    // Ensure either `subscriptionId` or `userId` is provided
    if (!subscriptionId) {
        return res
            .status(400)
            .json({ message: "Please provide a valid subscriptionId or userId" });
    }

    try {
        let deletedSubscription;

        // Delete subscription by subscriptionId if provided
        if (subscriptionId) {
            deletedSubscription = await prisma.subscriptionRequest.delete({
                where: { id: parseInt(subscriptionId) }
            });
        } 
   

        // Check if the subscription was deleted
        if (!deletedSubscription) {
            return res.status(404).json({ message: "Subscription not found" });
        }

        return res.status(200).json({ message: "Subscription deleted successfully" });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};




const getAdminDashBoardStats = async (req, res) => {
    try {
        // Fetch the data
        const users = await prisma.user.findMany({});
        const subscription = await prisma.subscriptionRequest.findMany({});
        const lesson = await prisma.lesson.findMany({});

        // Send the length of each collection as the response
        res.status(200).json({
            usersCount: users.length,
            subscriptionCount: subscription.length,
            lessonCount: lesson.length,
        });
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};





module.exports = {
    getUserInfo,updateOrCreateScore,getAllScoresWithUsers ,getSubscriptionRequests ,addSubscription,getAllUser,getUserScore,getAdminDashBoardStats,deleteSubscription
}