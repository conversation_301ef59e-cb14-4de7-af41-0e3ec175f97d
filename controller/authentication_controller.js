const prisma = require("../prisma/index");
const { sendSms } = require("../utils/sendOtp");
const bcrypt = require("bcrypt");
require("dotenv").config();

const testServer = async (req, res) => {
return res.status(200).json({ message: 'okay' });
};


const handleLogin = async (req, res) => {
  const { phone, password } = req.body;

  if (!phone) {
    return res
      .status(400)
      .json({ message: "Phone number and Password required!!" });
  }

  const user = await prisma.user.findUnique({
    where: {
      phone: phone,
    }
  });

  if (!user) {
    return res.status(401).json({ message: "User not found!" });
  }

  const match = await bcrypt.compare(password, user.password);

  if (match) {
    return res.status(200).json({ message: user.id });
  } else {
    return res.status(401).json({ message: "Worng password!" });
  }
};


const sendOtp = async (req, res) => {
  const phone = req.query.phone;

  try {
      if(phone=='01858573112'){
            res.status(200).json({ message: `1234` });  
      }
      
    const num = Math.floor(1000 + Math.random() * 9000); // Generate OTP as a number
    const otpString = num.toString();// Convert OTP number to a string

    console.log(`${otpString}`);
    const otp = await bcrypt.hash(otpString, 10); // Hash the OTP string

    const apiKey = 'wyEftUHaMBMPS4DrUm2t';
    const senderId = '8809617617760';
    const phoneNumbers = `88${phone}`;
    const message = `Welcome to News Lexica, your OTP is ${num}`;
    sendSms(apiKey, senderId, phoneNumbers, message)
      .then((response) => {
        console.log('SMS sent successfully:', response);
        res.status(200).json({ message: `${otp}` });
      })
      .catch((error) => {
        console.error('Error sending SMS:', error.message);
        res.status(500).json({ error: 'Error sending SMS' });
      });

  } catch (err) {
    console.error('Error:', err);
    res.status(500).json({ error: `${err}` });
  }
}



const verifyOtp = async (req, res) => {
  const secretOtp = req.query.secretOtp;
  const enteredOtp = req.query.enteredOtp; // OTP entered by the user
  try {
    const comp = bcrypt.compareSync(enteredOtp, secretOtp);
    if (comp) {
      res.status(200).json({ message: true });
    }
    else {
      res.status(400).json({ message: 'Otp not matched' });
    }

  } catch (err) {
    console.error('Error verifying OTP:', err);
    res.status(500).json({ error: `${err}` });
  }
}

module.exports = { handleLogin, sendOtp, verifyOtp,testServer };