// Test setup file
require('dotenv').config({ path: '.env.test' });

// Global test timeout
jest.setTimeout(10000);

// Mock console methods in tests to reduce noise
global.console = {
  ...console,
  // Uncomment to ignore specific console methods in tests
  // log: jest.fn(),
  // debug: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test utilities
global.testUtils = {
  // Helper to create test user data
  createTestUser: () => ({
    name: 'Test User',
    phone: '01712345678',
    password: 'testpassword123'
  }),
  
  // Helper to create test lesson data
  createTestLesson: () => ({
    lessonTitle: 'Test Lesson',
    title: 'Test Article Title',
    lessonNumber: 1,
    paragraph: 'This is a test paragraph for the lesson.',
    part: 1,
    pressName: 'Test Press',
    publishDate: '2024-01-01',
    contentType: 1,
    category: 'news'
  }),
  
  // Helper to create test quiz data
  createTestQuiz: () => ({
    question: 'What is the capital of Bangladesh?',
    option1: 'Dhaka',
    option2: 'Chittagong',
    option3: 'Sylhet',
    option4: 'Rajshahi',
    answer: 'Dhaka',
    lessonId: 1
  }),
  
  // Helper to generate valid JWT token for testing
  generateTestToken: (userId = 1, roles = ['USER']) => {
    const jwt = require('jsonwebtoken');
    const config = require('../config/environment');
    return jwt.sign(
      { id: userId, roles },
      config.jwt.accessTokenSecret,
      { expiresIn: '1h' }
    );
  },
  
  // Helper to generate admin token
  generateAdminToken: (userId = 1) => {
    return global.testUtils.generateTestToken(userId, ['ADMIN']);
  }
};

// Database cleanup helper
global.cleanupDatabase = async () => {
  const { PrismaClient } = require('@prisma/client');
  const prisma = new PrismaClient();
  
  try {
    // Clean up test data in reverse order of dependencies
    await prisma.vote.deleteMany({});
    await prisma.lessonDiscussion.deleteMany({});
    await prisma.quizOne.deleteMany({});
    await prisma.quizTwo.deleteMany({});
    await prisma.quizThree.deleteMany({});
    await prisma.greQuizes.deleteMany({});
    await prisma.examQuizes.deleteMany({});
    await prisma.lesson.deleteMany({});
    await prisma.greLesson.deleteMany({});
    await prisma.examLesson.deleteMany({});
    await prisma.promotion.deleteMany({});
    await prisma.user.deleteMany({
      where: {
        phone: {
          startsWith: '0171' // Only delete test users
        }
      }
    });
  } catch (error) {
    console.error('Database cleanup error:', error);
  } finally {
    await prisma.$disconnect();
  }
};

// Setup and teardown hooks
beforeAll(async () => {
  // Any global setup
});

afterAll(async () => {
  // Global cleanup
  await global.cleanupDatabase();
});

// Mock external services for testing
jest.mock('../utils/sendOtp', () => ({
  sendSms: jest.fn().mockResolvedValue({ success: true, message: 'SMS sent' })
}));
