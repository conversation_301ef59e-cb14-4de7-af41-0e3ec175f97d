const prisma = require("../../prisma/index");
const config = require("../../config/environment");

/**
 * Regular Lessons Controller
 * Handles CRUD operations for news article lessons
 */

const createLesson = async (req, res) => {
  try {
    const {
      lessonTitle,
      title,
      lessonNumber,
      paragraph,
      part,
      pressName,
      publishDate,
      contentType,
      category,
    } = req.body;

    const lesson = await prisma.lesson.create({
      data: {
        lessonTitle,
        lessonNumber: parseInt(lessonNumber),
        paragraph,
        pressName,
        part: parseInt(part),
        publishDate,
        title,
        contentType: parseInt(contentType),
        category,
      },
    });

    res.status(201).json({
      success: true,
      message: "Lesson created successfully",
      data: lesson,
    });
  } catch (error) {
    console.error("Error creating lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create lesson",
      error: error.message,
    });
  }
};

const getLessons = async (req, res) => {
  try {
    const { 
      contentType, 
      category, 
      part, 
      page = 1, 
      limit = 10 
    } = req.query;
    
    let whereClause = {};
    if (contentType) whereClause.contentType = parseInt(contentType);
    if (part) whereClause.part = parseInt(part);
    if (category) whereClause.category = category;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const [lessons, totalCount] = await Promise.all([
      prisma.lesson.findMany({
        where: whereClause,
        select: {
          id: true,
          lessonNumber: true,
          category: true,
          title: true,
          pressName: true,
          createdAt: true,
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limitNum,
      }),
      prisma.lesson.count({ where: whereClause }),
    ]);

    res.status(200).json({
      success: true,
      data: lessons,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
      },
    });
  } catch (error) {
    console.error("Error fetching lessons:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch lessons",
      error: error.message,
    });
  }
};

const getLessonById = async (req, res) => {
  try {
    const { id } = req.params;

    const lesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) },
      include: {
        QuizOne: true,
        QuizTwo: true,
        QuizThree: true,
      },
    });

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: "Lesson not found",
      });
    }

    res.status(200).json({
      success: true,
      data: lesson,
    });
  } catch (error) {
    console.error("Error fetching lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch lesson",
      error: error.message,
    });
  }
};

const updateLesson = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      lessonTitle,
      title,
      lessonNumber,
      paragraph,
      part,
      pressName,
      publishDate,
      contentType,
      category,
    } = req.body;

    const existingLesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingLesson) {
      return res.status(404).json({
        success: false,
        message: "Lesson not found",
      });
    }

    const updatedLesson = await prisma.lesson.update({
      where: { id: parseInt(id) },
      data: {
        lessonTitle,
        lessonNumber: parseInt(lessonNumber),
        paragraph,
        pressName,
        part: parseInt(part),
        publishDate,
        title,
        contentType: parseInt(contentType),
        category,
      },
    });

    res.status(200).json({
      success: true,
      message: "Lesson updated successfully",
      data: updatedLesson,
    });
  } catch (error) {
    console.error("Error updating lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update lesson",
      error: error.message,
    });
  }
};

const deleteLesson = async (req, res) => {
  try {
    const { id } = req.params;

    const existingLesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingLesson) {
      return res.status(404).json({
        success: false,
        message: "Lesson not found",
      });
    }

    // Delete related quizzes first
    await Promise.all([
      prisma.quizOne.deleteMany({ where: { lessonId: parseInt(id) } }),
      prisma.quizTwo.deleteMany({ where: { lessonId: parseInt(id) } }),
      prisma.quizThree.deleteMany({ where: { lessonId: parseInt(id) } }),
    ]);

    // Delete the lesson
    await prisma.lesson.delete({
      where: { id: parseInt(id) },
    });

    res.status(200).json({
      success: true,
      message: "Lesson deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete lesson",
      error: error.message,
    });
  }
};

module.exports = {
  createLesson,
  getLessons,
  getLessonById,
  updateLesson,
  deleteLesson,
};
