const express = require("express");
const { uploadAudio } = require("../../utils/audioUploader");
const { 
    getBooksList, addSingleBook, getSingleBook, updateBook, deleteBook,
    getReadingLists, addReadingList, getSingleReadingList, updateReadingList,
    deleteReadingList, getReadingPassages, addReadingPassage, getSingleReadingPassage, 
    updateReadingPassage, deleteReadingPassage, addQuizOne, getQuizzesOne, updateQuizOne,
    deleteQuizOne, getQuizzesTwo, addQuizTwo, getSingleQuizTwo, updateQuizTwo, 
    deleteQuizTwo, addPassage, updatePassage, deletePassage, addQuizesExmple, 
    updateQuizExample, deleteQuizExample
} = require("../../controller/v2/ielts_module_controller");

const router = express.Router();


router.get("/books", getBooksList);
router.post("/books", addSingleBook);
router.get("/books/:id", getSingleBook);
router.put("/books/:id", updateBook);
router.delete("/books/:id", deleteBook);


router.get("/readingLists", getReadingLists);
router.post("/readingLists", addReadingList);
router.get("/readingLists/:id", getSingleReadingList);
router.put("/readingLists/:id", updateReadingList);
router.delete("/readingLists/:id", deleteReadingList);


router.get("/readingPassages", getReadingPassages);
router.post("/readingPassages", addReadingPassage);
router.get("/readingPassages/:id", getSingleReadingPassage);
router.put("/readingPassages/:id", updateReadingPassage);
router.delete("/readingPassages/:id", deleteReadingPassage);


router.get("/quizzesOne", getQuizzesOne);
router.post("/quizzesOne/:id", addQuizOne);
router.put("/quizzesOne/:id", updateQuizOne);
router.delete("/quizzesOne/:id", deleteQuizOne);


router.get("/quizzesTwo", getQuizzesTwo);
router.post("/quizzesTwo/:id", addQuizTwo);
router.get("/quizzesTwo/:id", getSingleQuizTwo);
router.put("/quizzesTwo/:id", updateQuizTwo);
router.delete("/quizzesTwo/:id", deleteQuizTwo);

router.post("/passage", uploadAudio, addPassage);
router.put("/passage/:id", uploadAudio, updatePassage);
router.delete("/passage/:id", deletePassage);


router.post("/quizzesExample/:id", addQuizesExmple);
router.put("/quizzesExample/:id", updateQuizExample);
router.delete("/quizzesExample/:id", deleteQuizExample);


module.exports = router;
