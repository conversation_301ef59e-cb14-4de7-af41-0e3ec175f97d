const express = require("express");
const router = express.Router();
const {
  registerNewUser,
  checkUserExist,
  changeUserPassword,
} = require("../../controller/v2/register_controller");
const {
  validateBody,
  validateQuery,
} = require("../../middleware/validateRequest");
const {
  authLimiter,
  passwordResetLimiter,
} = require("../../middleware/rateLimiter");
const createUserSchema = require("../../validator/userValidator.js");
const { passwordChangeSchema } = require("../../validators/authValidators");
const Joi = require("joi");

// Schema for checking user existence
const checkUserSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^01[3-9]\d{8}$/)
    .required()
    .messages({
      "string.pattern.base":
        "Phone number must be a valid Bangladesh mobile number",
      "any.required": "Phone number is required",
    }),
});

router.post("/", authLimiter, validateBody(createUserSchema), registerNewUser);
router.post(
  "/changeUserPassword",
  passwordResetLimiter,
  validateBody(passwordChangeSchema),
  changeUserPassword
);
router.get("/userExist", validateQuery(checkUserSchema), checkUserExist);

module.exports = router;
