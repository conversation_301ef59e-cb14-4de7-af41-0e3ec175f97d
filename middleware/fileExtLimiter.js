const path = require("path");

const fileExtLimiter = (allowedExts) => {
  return (req, res, next) => {
    const files = req.files;

    const fileExtensions = [];
    Object.keys(files).forEach((key) => {
      fileExtensions.push(path.extname(files[key].name));
    });

    const allowed = fileExtensions.every((ext) => allowedExts.includes(ext));

    if (!allowed) {
      const message =
        `Upload failed. Only ${allowedExts.toString()} files allowed.`.replaceAll(
          ",",
          ", "
        );

      return res.status(422).json({ status: "error", message });
    }

    next();
  };
};

module.exports = { fileExtLimiter };
