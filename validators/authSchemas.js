const Joi = require('joi');

// Common validation patterns
const phonePattern = /^01[3-9]\d{8}$/; // Bangladesh phone number pattern
const passwordPattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/; // Strong password

// Authentication schemas
const authSchemas = {
  // Login validation
  login: Joi.object({
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
    password: Joi.string()
      .min(6)
      .max(50)
      .required()
      .messages({
        'string.min': 'Password must be at least 6 characters long',
        'string.max': 'Password must not exceed 50 characters',
        'any.required': 'Password is required',
      }),
    token: Joi.string().optional(),
  }),

  // Admin login validation
  adminLogin: Joi.object({
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
    password: Joi.string()
      .min(6)
      .max(50)
      .required()
      .messages({
        'string.min': 'Password must be at least 6 characters long',
        'string.max': 'Password must not exceed 50 characters',
        'any.required': 'Password is required',
      }),
    token: Joi.string().optional(),
  }),

  // OTP send validation
  sendOtp: Joi.object({
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
  }),

  // OTP verification validation
  verifyOtp: Joi.object({
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
    enteredOtp: Joi.string()
      .length(4)
      .pattern(/^\d{4}$/)
      .required()
      .messages({
        'string.length': 'OTP must be exactly 4 digits',
        'string.pattern.base': 'OTP must contain only numbers',
        'any.required': 'OTP is required',
      }),
    hashedOtp: Joi.string()
      .required()
      .messages({
        'any.required': 'Hashed OTP is required',
      }),
  }),

  // Token refresh validation
  refreshToken: Joi.object({
    refreshToken: Joi.string()
      .required()
      .messages({
        'any.required': 'Refresh token is required',
      }),
  }),

  // User registration validation
  register: Joi.object({
    name: Joi.string()
      .min(2)
      .max(50)
      .trim()
      .required()
      .messages({
        'string.min': 'Name must be at least 2 characters long',
        'string.max': 'Name must not exceed 50 characters',
        'any.required': 'Name is required',
      }),
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
    password: Joi.string()
      .min(8)
      .max(50)
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters long',
        'string.max': 'Password must not exceed 50 characters',
        'any.required': 'Password is required',
      }),
    confirmPassword: Joi.string()
      .valid(Joi.ref('password'))
      .required()
      .messages({
        'any.only': 'Confirm password must match password',
        'any.required': 'Confirm password is required',
      }),
  }),

  // Password change validation
  changePassword: Joi.object({
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
    newPassword: Joi.string()
      .min(8)
      .max(50)
      .required()
      .messages({
        'string.min': 'New password must be at least 8 characters long',
        'string.max': 'New password must not exceed 50 characters',
        'any.required': 'New password is required',
      }),
    confirmPassword: Joi.string()
      .valid(Joi.ref('newPassword'))
      .required()
      .messages({
        'any.only': 'Confirm password must match new password',
        'any.required': 'Confirm password is required',
      }),
  }),

  // User existence check validation
  userExists: Joi.object({
    phone: Joi.string()
      .pattern(phonePattern)
      .required()
      .messages({
        'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
        'any.required': 'Phone number is required',
      }),
  }),
};

// Query parameter schemas
const authQuerySchemas = {
  // Pagination for user lists
  userList: Joi.object({
    page: Joi.number()
      .integer()
      .positive()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.positive': 'Page must be positive',
      }),
    limit: Joi.number()
      .integer()
      .positive()
      .max(100)
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.positive': 'Limit must be positive',
        'number.max': 'Limit must not exceed 100',
      }),
    search: Joi.string()
      .min(1)
      .max(50)
      .optional()
      .messages({
        'string.min': 'Search term must be at least 1 character',
        'string.max': 'Search term must not exceed 50 characters',
      }),
  }),
};

// Parameter schemas
const authParamSchemas = {
  // User ID parameter
  userId: Joi.object({
    id: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'User ID must be a number',
        'number.integer': 'User ID must be an integer',
        'number.positive': 'User ID must be positive',
        'any.required': 'User ID is required',
      }),
  }),
};

module.exports = {
  authSchemas,
  authQuerySchemas,
  authParamSchemas,
};
