const express = require("express");
const router = express.Router();
const { getUserInfo,updateOrCreateScore,getAllScoresWithUsers,getSubscriptionRequests,addSubscription,getAllUser,getUserScore,getAdminDashBoardStats,deleteSubscription,updateSubscription } = require("../../controller/v2/user_controller");
const verifyJWT = require("../../middleware/verifyJWT");
const { verifyRoles } = require("../../middleware/verifyRoles");
router.get("/",verifyJWT,getUserInfo);
router.post("/updateScoreById",verifyJWT, updateOrCreateScore);
router.post("/getScoreList", getAllScoresWithUsers);
router.get("/subscription", getSubscriptionRequests);
router.post("/subscription", addSubscription);
router.patch("/subscription/:id", updateSubscription).delete("/subscription/:id", deleteSubscription);
router.get("/getAllUser", getAllUser);
router.post("/getUserScore", getUserScore);
router.get("/getAdminDashBoardStats", getAdminDashBoardStats);

module.exports = router;