const prisma = require("../../prisma/index");
const { calculateSubscription } = require('../../utils/calculate_subcription');
const getUserInfo = async (req, res) => {
    const id = req.user;
  
    try {
        const user = await prisma.user.findUnique({
            where: {
                id: parseInt(id)
            },
            include: {
                SubscriptionRequest: {
                    select:{
                    subscribed:true,
                    createdAt:true,
                    duration:true
                    }
                },
                ScoreBoard:{
                    select:{
                        score:true
                    }
                }
            }
        });
      if(user.SubscriptionRequest)
      {
        const subsciption=calculateSubscription(user.SubscriptionRequest.createdAt,user.SubscriptionRequest.duration);
        user.SubscriptionRequest.subscribed=subsciption;
        const subscriptionStatus=subsciption?"ACTIVE":"EXPIRED";
        user.SubscriptionRequest['status']=subscriptionStatus;
      }
        return res
            .status(200)
            .json({ message: user });

    }
    catch (err) {
        console.error("Error", err);

        return res
            .status(400)
            .json({ message: err });
    }
}



const updateOrCreateScore = async (req, res) => {
    const {score } = req.body;
    const userId = req.user;
    if (!userId || !score || isNaN(parseInt(score))) {
        return res.status(400).json({ message: "Valid userId and score must be provided" });
    }

    const parsedUserId = parseInt(userId);
    const parsedScore = parseInt(score);

    try {
        const existingScoreBoard = await prisma.scoreBoard.findUnique({
            where: { userId: parsedUserId }
        });

        let updatedScoreBoard;

        if (existingScoreBoard) {
            // Update the existing score
            updatedScoreBoard = await prisma.scoreBoard.update({
                where: { userId: parsedUserId },
                data: { score: existingScoreBoard.score + parsedScore }
            });
        } else {
            // Create a new score board entry
            updatedScoreBoard = await prisma.scoreBoard.create({
                data: {
                    userId: parsedUserId,
                    score: parsedScore
                }
            });
        }

        return res.status(200).json({ message: updatedScoreBoard });
    } catch (err) {
        console.error("Error updating or creating score:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};


const getUserScore = async (req, res) => {
    const { userId } = req.query;

    if (!userId || isNaN(parseInt(userId))) {
        return res.status(400).json({ message: "Valid userId must be provided" });
    }

    const parsedUserId = parseInt(userId);

    try {
        const scoreBoard = await prisma.scoreBoard.findUnique({
            where: { userId: parsedUserId }
        });

        if (!scoreBoard) {
            return res.status(404).json({ message: "User score not found" });
        }

        return res.status(200).json({ userId: parsedUserId, score: scoreBoard.score });
    } catch (err) {
        console.error("Error fetching user score:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};




const getAllScoresWithUsers = async (req, res) => {
    const { page = 1, pageSize = 10 } = req.query;

    const parsedPage = parseInt(page);
    const parsedPageSize = parseInt(pageSize);

    if (isNaN(parsedPage) || isNaN(parsedPageSize) || parsedPage <= 0 || parsedPageSize <= 0) {
        return res.status(400).json({ message: "Invalid page or pageSize parameters" });
    }

    try {
        const totalItems = await prisma.scoreBoard.count();

        const scores = await prisma.scoreBoard.findMany({
            skip: (parsedPage - 1) * parsedPageSize,
            take: parsedPageSize,
            orderBy: {
                score: 'desc'
            },
            include: {
                user: true // This will include user details with each score record
            }
        });

        return res.status(200).json({
            scores,
            totalItems,
            totalPages: Math.ceil(totalItems / parsedPageSize),
            currentPage: parsedPage
        });
    } catch (err) {
        console.error("Error fetching scores with users:", err);
        res.status(500).json({ message: "Internal server error" });
    }
};






const getSubscriptionRequests = async (req, res) => {
    try {
        let { page, limit, q } = req.query;

        // Convert to integers and set defaults
        page = parseInt(page) || 1;
        limit = parseInt(limit) || 10;
        const skip = (page - 1) * limit;

        // Build the where condition
        const where = q
            ? {
                subscrioberPhone: {
                      contains: q,
                  },
              }
            : {};

        // Fetch filtered and paginated subscriptions
        const subscriptions = await prisma.subscriptionRequest.findMany({
            where,
            skip,
            take: limit,
        });

        // Get filtered total count
        const totalCount = await prisma.subscriptionRequest.count({ where });

        return res.status(200).json({
            data: subscriptions,
            pagination: {
                total: totalCount,
                page,
                limit,
                totalPages: Math.ceil(totalCount / limit),
            },
        });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};


const getAllUser = async (req, res) => {
    try {
        const { limit = 10, page = 1, q = '' } = req.query;

        const users = await prisma.user.findMany({
            where: {
                phone: {
                    contains: q,
                },
            },
            select:{
                id:true,
                name:true,
                phone:true,
                photo:true
            },
            take: parseInt(limit),
            skip: (parseInt(page) - 1) * parseInt(limit),
        });

        const totalUsers = await prisma.user.count({
            where: {
                phone: {
                    contains: q,
                },
              
            },
        });

        return res.status(200).json({ 
            message: 'Users retrieved successfully',
            data: users,
            total: totalUsers,
            page: parseInt(page),
            limit: parseInt(limit)
        });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};




const addSubscription = async (req, res) => {
    const { subscrioberPhone, userId, amount, duration, subscribed } = req.body;

    if (!subscrioberPhone || !userId || !amount || !duration || typeof subscribed === 'undefined') {
        return res
            .status(400)
            .json({ message: "Please provide valid credentials" });
    }

    try {
        
        
        const subscription = await prisma.subscriptionRequest.create({
            data: {
                subscrioberPhone,
                userId:parseInt(userId),
                amount,
                duration,
                subscribed
            }
        });

        return res.status(201).json({ message: subscription });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};


const updateSubscription = async (req, res) => {
    const { id } = req.params;
    const { subscrioberPhone, amount, duration, subscribed } = req.body;

    if (!id) {
        return res.status(400).json({ message: "Subscription ID is required" });
    }

    try {
        const existingSubscription = await prisma.subscriptionRequest.findUnique({
            where: { id: parseInt(id) },
        });

        if (!existingSubscription) {
            return res.status(404).json({ message: "Subscription not found" });
        }

        const updatedSubscription = await prisma.subscriptionRequest.update({
            where: { id: parseInt(id) },
            data: {
                subscrioberPhone,
                amount,
                duration,
                subscribed,
            },
        });

        return res.status(200).json({ message: "Subscription updated successfully", subscription: updatedSubscription });
    } catch (err) {
        return res.status(500).json({ message: err.message });
    }
};




const deleteSubscription = async (req, res) => {
    const { id } = req.params;

    // Ensure either `subscriptionId` or `userId` is provided


    try {
        let deletedSubscription;

        deletedSubscription = await prisma.subscriptionRequest.delete({
            where: { id: parseInt(id) }
        });
   

        // Check if the subscription was deleted
        if (!deletedSubscription) {
            return res.status(404).json({ message: "Subscription not found" });
        }

        return res.status(200).json({ message: "Subscription deleted successfully" });
    } catch (err) {
        return res.status(400).json({ message: err.message });
    }
};




const getAdminDashBoardStats = async (req, res) => {
    try {
        // Fetch the data
        const totalUsers = await prisma.user.count();
        const newUsers = await prisma.user.count({
            where: {
                createdAt: {
                    gte: new Date(new Date().setDate(new Date().getDate() - 7)) // Count users created in the last 7 days
                }
            }
        });
        const totalSubscribers = await prisma.subscriptionRequest.count();
        const totalQuizzes = await prisma.lesson.count({
        });

        // Send the updated response
        res.status(200).json({
            totalUsers,
            newUsers,
            totalSubscribers,
            totalQuizzes,
        });
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        res.status(500).json({ message: 'Internal Server Error' });
    }
};




module.exports = {
    getUserInfo,updateOrCreateScore,getAllScoresWithUsers ,getSubscriptionRequests ,addSubscription,getAllUser,getUserScore,getAdminDashBoardStats,deleteSubscription,updateSubscription
}