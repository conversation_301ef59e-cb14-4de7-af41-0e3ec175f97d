const express = require("express");
const router = express.Router();
const {
  registerNewUser,
  checkUserExist,
  changeUserPassword,
} = require("../../controller/v2/register_controller");
const {
  validateBody,
  validateQuery,
} = require("../../middleware/validateRequest");
const {
  authLimiter,
  passwordResetLimiter,
} = require("../../middleware/rateLimiter");
const createUserSchema = require("../../validator/userValidator.js");
const { passwordChangeSchema } = require("../../validators/authValidators");
const Joi = require("joi");

// Schema for checking user existence
const checkUserSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^01[3-9]\d{8}$/)
    .required()
    .messages({
      "string.pattern.base":
        "Phone number must be a valid Bangladesh mobile number",
      "any.required": "Phone number is required",
    }),
});

/**
 * @route   POST /api/v2/users/register
 * @desc    Register a new user
 * @access  Public
 * @body    { name: string, phone: string, password: string }
 */
router.post(
  "/register",
  authLimiter,
  validateBody(createUserSchema),
  registerNewUser
);

/**
 * @route   POST /api/v2/users/password/change
 * @desc    Change user password
 * @access  Public
 * @body    { phone: string, currentPassword: string, newPassword: string }
 */
router.post(
  "/password/change",
  passwordResetLimiter,
  validateBody(passwordChangeSchema),
  changeUserPassword
);

/**
 * @route   GET /api/v2/users/exists
 * @desc    Check if user exists by phone number
 * @access  Public
 * @query   ?phone=01XXXXXXXXX
 */
router.get("/exists", validateQuery(checkUserSchema), checkUserExist);

module.exports = router;
