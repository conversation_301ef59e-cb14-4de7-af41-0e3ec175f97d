const Joi = require('joi');

// Lesson validation schemas
const lessonSchemas = {
  // Create lesson validation
  createLesson: Joi.object({
    lessonTitle: Joi.string()
      .min(3)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Lesson title must be at least 3 characters long',
        'string.max': 'Lesson title must not exceed 200 characters',
        'any.required': 'Lesson title is required',
      }),
    title: Joi.string()
      .min(3)
      .max(300)
      .trim()
      .required()
      .messages({
        'string.min': 'Article title must be at least 3 characters long',
        'string.max': 'Article title must not exceed 300 characters',
        'any.required': 'Article title is required',
      }),
    lessonNumber: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'Lesson number must be a number',
        'number.integer': 'Lesson number must be an integer',
        'number.positive': 'Lesson number must be positive',
        'any.required': 'Lesson number is required',
      }),
    paragraph: Joi.string()
      .min(10)
      .max(10000)
      .trim()
      .required()
      .messages({
        'string.min': 'Paragraph must be at least 10 characters long',
        'string.max': 'Paragraph must not exceed 10,000 characters',
        'any.required': 'Paragraph is required',
      }),
    part: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'Part must be a number',
        'number.integer': 'Part must be an integer',
        'number.positive': 'Part must be positive',
        'any.required': 'Part is required',
      }),
    pressName: Joi.string()
      .min(2)
      .max(100)
      .trim()
      .required()
      .messages({
        'string.min': 'Press name must be at least 2 characters long',
        'string.max': 'Press name must not exceed 100 characters',
        'any.required': 'Press name is required',
      }),
    publishDate: Joi.date()
      .iso()
      .required()
      .messages({
        'date.base': 'Publish date must be a valid date',
        'date.format': 'Publish date must be in ISO format (YYYY-MM-DD)',
        'any.required': 'Publish date is required',
      }),
    contentType: Joi.number()
      .integer()
      .valid(1, 2, 3, 4, 5)
      .required()
      .messages({
        'number.base': 'Content type must be a number',
        'number.integer': 'Content type must be an integer',
        'any.only': 'Content type must be one of: 1, 2, 3, 4, 5',
        'any.required': 'Content type is required',
      }),
    category: Joi.string()
      .valid('news', 'sports', 'technology', 'business', 'entertainment', 'health', 'science', 'politics')
      .required()
      .messages({
        'any.only': 'Category must be one of: news, sports, technology, business, entertainment, health, science, politics',
        'any.required': 'Category is required',
      }),
    audioFile: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': 'Audio file must be a valid URL',
      }),
    imageUrl: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': 'Image URL must be a valid URL',
      }),
    difficulty: Joi.string()
      .valid('beginner', 'intermediate', 'advanced')
      .default('intermediate')
      .messages({
        'any.only': 'Difficulty must be one of: beginner, intermediate, advanced',
      }),
    tags: Joi.array()
      .items(Joi.string().min(1).max(50))
      .max(10)
      .optional()
      .messages({
        'array.max': 'Maximum 10 tags allowed',
        'string.min': 'Each tag must be at least 1 character',
        'string.max': 'Each tag must not exceed 50 characters',
      }),
  }),

  // Update lesson validation (all fields optional)
  updateLesson: Joi.object({
    lessonTitle: Joi.string()
      .min(3)
      .max(200)
      .trim()
      .optional()
      .messages({
        'string.min': 'Lesson title must be at least 3 characters long',
        'string.max': 'Lesson title must not exceed 200 characters',
      }),
    title: Joi.string()
      .min(3)
      .max(300)
      .trim()
      .optional()
      .messages({
        'string.min': 'Article title must be at least 3 characters long',
        'string.max': 'Article title must not exceed 300 characters',
      }),
    lessonNumber: Joi.number()
      .integer()
      .positive()
      .optional()
      .messages({
        'number.base': 'Lesson number must be a number',
        'number.integer': 'Lesson number must be an integer',
        'number.positive': 'Lesson number must be positive',
      }),
    paragraph: Joi.string()
      .min(10)
      .max(10000)
      .trim()
      .optional()
      .messages({
        'string.min': 'Paragraph must be at least 10 characters long',
        'string.max': 'Paragraph must not exceed 10,000 characters',
      }),
    part: Joi.number()
      .integer()
      .positive()
      .optional()
      .messages({
        'number.base': 'Part must be a number',
        'number.integer': 'Part must be an integer',
        'number.positive': 'Part must be positive',
      }),
    pressName: Joi.string()
      .min(2)
      .max(100)
      .trim()
      .optional()
      .messages({
        'string.min': 'Press name must be at least 2 characters long',
        'string.max': 'Press name must not exceed 100 characters',
      }),
    publishDate: Joi.date()
      .iso()
      .optional()
      .messages({
        'date.base': 'Publish date must be a valid date',
        'date.format': 'Publish date must be in ISO format (YYYY-MM-DD)',
      }),
    contentType: Joi.number()
      .integer()
      .valid(1, 2, 3, 4, 5)
      .optional()
      .messages({
        'number.base': 'Content type must be a number',
        'number.integer': 'Content type must be an integer',
        'any.only': 'Content type must be one of: 1, 2, 3, 4, 5',
      }),
    category: Joi.string()
      .valid('news', 'sports', 'technology', 'business', 'entertainment', 'health', 'science', 'politics')
      .optional()
      .messages({
        'any.only': 'Category must be one of: news, sports, technology, business, entertainment, health, science, politics',
      }),
    audioFile: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': 'Audio file must be a valid URL',
      }),
    imageUrl: Joi.string()
      .uri()
      .optional()
      .messages({
        'string.uri': 'Image URL must be a valid URL',
      }),
    difficulty: Joi.string()
      .valid('beginner', 'intermediate', 'advanced')
      .optional()
      .messages({
        'any.only': 'Difficulty must be one of: beginner, intermediate, advanced',
      }),
    tags: Joi.array()
      .items(Joi.string().min(1).max(50))
      .max(10)
      .optional()
      .messages({
        'array.max': 'Maximum 10 tags allowed',
        'string.min': 'Each tag must be at least 1 character',
        'string.max': 'Each tag must not exceed 50 characters',
      }),
  }).min(1).messages({
    'object.min': 'At least one field must be provided for update',
  }),
};

// Query parameter schemas for lessons
const lessonQuerySchemas = {
  // Get lessons with filtering and pagination
  getLessons: Joi.object({
    page: Joi.number()
      .integer()
      .positive()
      .default(1)
      .messages({
        'number.base': 'Page must be a number',
        'number.integer': 'Page must be an integer',
        'number.positive': 'Page must be positive',
      }),
    limit: Joi.number()
      .integer()
      .positive()
      .max(100)
      .default(10)
      .messages({
        'number.base': 'Limit must be a number',
        'number.integer': 'Limit must be an integer',
        'number.positive': 'Limit must be positive',
        'number.max': 'Limit must not exceed 100',
      }),
    contentType: Joi.number()
      .integer()
      .valid(1, 2, 3, 4, 5)
      .optional()
      .messages({
        'number.base': 'Content type must be a number',
        'number.integer': 'Content type must be an integer',
        'any.only': 'Content type must be one of: 1, 2, 3, 4, 5',
      }),
    category: Joi.string()
      .valid('news', 'sports', 'technology', 'business', 'entertainment', 'health', 'science', 'politics')
      .optional()
      .messages({
        'any.only': 'Category must be one of: news, sports, technology, business, entertainment, health, science, politics',
      }),
    part: Joi.number()
      .integer()
      .positive()
      .optional()
      .messages({
        'number.base': 'Part must be a number',
        'number.integer': 'Part must be an integer',
        'number.positive': 'Part must be positive',
      }),
    difficulty: Joi.string()
      .valid('beginner', 'intermediate', 'advanced')
      .optional()
      .messages({
        'any.only': 'Difficulty must be one of: beginner, intermediate, advanced',
      }),
    search: Joi.string()
      .min(1)
      .max(100)
      .optional()
      .messages({
        'string.min': 'Search term must be at least 1 character',
        'string.max': 'Search term must not exceed 100 characters',
      }),
    sortBy: Joi.string()
      .valid('createdAt', 'lessonNumber', 'title', 'publishDate')
      .default('createdAt')
      .messages({
        'any.only': 'Sort by must be one of: createdAt, lessonNumber, title, publishDate',
      }),
    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .default('desc')
      .messages({
        'any.only': 'Sort order must be either asc or desc',
      }),
  }),
};

// Parameter schemas for lessons
const lessonParamSchemas = {
  // Lesson ID parameter
  lessonId: Joi.object({
    id: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'Lesson ID must be a number',
        'number.integer': 'Lesson ID must be an integer',
        'number.positive': 'Lesson ID must be positive',
        'any.required': 'Lesson ID is required',
      }),
  }),
};

module.exports = {
  lessonSchemas,
  lessonQuerySchemas,
  lessonParamSchemas,
};
