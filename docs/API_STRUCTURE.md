# News Lexica Backend - Refactored API Structure

## Overview

The API has been refactored to follow RESTful conventions and proper separation of concerns. The controllers have been split into focused, single-responsibility modules.

## New API Structure

### Authentication Routes

**Base URL:** `/api/v2/auth`

| Method | Endpoint         | Description          | Access |
| ------ | ---------------- | -------------------- | ------ |
| GET    | `/health`        | Test server health   | Public |
| POST   | `/login`         | User login           | Public |
| POST   | `/admin/login`   | Admin login          | Public |
| POST   | `/otp/send`      | Send OTP to phone    | Public |
| POST   | `/otp/verify`    | Verify OTP           | Public |
| POST   | `/token/refresh` | Refresh access token | Public |

### User Management Routes

**Base URL:** `/api/v2/users`

| Method | Endpoint           | Description          | Access |
| ------ | ------------------ | -------------------- | ------ |
| POST   | `/register`        | Register new user    | Public |
| POST   | `/password/change` | Change user password | Public |
| GET    | `/exists`          | Check if user exists | Public |

### Lessons Routes

**Base URL:** `/api/v2/lessons`

| Method | Endpoint | Description                      | Access |
| ------ | -------- | -------------------------------- | ------ |
| GET    | `/`      | Get all lessons (with filtering) | Public |
| POST   | `/`      | Create new lesson                | Admin  |
| GET    | `/:id`   | Get lesson by ID                 | Public |
| PUT    | `/:id`   | Update lesson                    | Admin  |
| DELETE | `/:id`   | Delete lesson                    | Admin  |

**Query Parameters for GET /:**

- `contentType` - Filter by content type
- `category` - Filter by category
- `part` - Filter by part
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 10, max: 100)

### Quizzes Routes

**Base URL:** `/api/v2/quizzes`

| Method | Endpoint             | Description            | Access |
| ------ | -------------------- | ---------------------- | ------ |
| POST   | `/type-one`          | Create quiz type one   | Admin  |
| PUT    | `/type-one/:id`      | Update quiz type one   | Admin  |
| DELETE | `/type-one/:id`      | Delete quiz type one   | Admin  |
| POST   | `/type-two`          | Create quiz type two   | Admin  |
| PUT    | `/type-two/:id`      | Update quiz type two   | Admin  |
| DELETE | `/type-two/:id`      | Delete quiz type two   | Admin  |
| POST   | `/type-three`        | Create quiz type three | Admin  |
| PUT    | `/type-three/:id`    | Update quiz type three | Admin  |
| DELETE | `/type-three/:id`    | Delete quiz type three | Admin  |
| GET    | `/lessons/:lessonId` | Get quizzes by lesson  | Public |

**Query Parameters for GET /lessons/:lessonId:**

- `type` - Quiz type (quizOne, quizTwo, quizThree)

### GRE Lessons Routes

**Base URL:** `/api/v2/gre-lessons`

| Method | Endpoint                 | Description          | Access |
| ------ | ------------------------ | -------------------- | ------ |
| GET    | `/`                      | Get all GRE lessons  | Public |
| POST   | `/`                      | Create GRE lesson    | Admin  |
| GET    | `/:id`                   | Get GRE lesson by ID | Public |
| PUT    | `/:id`                   | Update GRE lesson    | Admin  |
| DELETE | `/:id`                   | Delete GRE lesson    | Admin  |
| GET    | `/:lessonId/quizzes`     | Get GRE quizzes      | Public |
| POST   | `/:lessonId/quizzes`     | Create GRE quiz      | Admin  |
| PUT    | `/:lessonId/quizzes/:id` | Update GRE quiz      | Admin  |
| DELETE | `/:lessonId/quizzes/:id` | Delete GRE quiz      | Admin  |

### Exam Lessons Routes

**Base URL:** `/api/v2/exam-lessons`

| Method | Endpoint                 | Description           | Access |
| ------ | ------------------------ | --------------------- | ------ |
| GET    | `/`                      | Get all exam lessons  | Public |
| POST   | `/`                      | Create exam lesson    | Admin  |
| GET    | `/:id`                   | Get exam lesson by ID | Public |
| PUT    | `/:id`                   | Update exam lesson    | Admin  |
| DELETE | `/:id`                   | Delete exam lesson    | Admin  |
| GET    | `/:lessonId/quizzes`     | Get exam quizzes      | Public |
| POST   | `/:lessonId/quizzes`     | Create exam quiz      | Admin  |
| PUT    | `/:lessonId/quizzes/:id` | Update exam quiz      | Admin  |
| DELETE | `/:lessonId/quizzes/:id` | Delete exam quiz      | Admin  |

### Promotions Routes

**Base URL:** `/api/v2/promotions`

| Method | Endpoint | Description                   | Access |
| ------ | -------- | ----------------------------- | ------ |
| GET    | `/`      | Get all promotions            | Public |
| POST   | `/`      | Create promotion (with image) | Admin  |
| GET    | `/:id`   | Get promotion by ID           | Public |
| PUT    | `/:id`   | Update promotion              | Admin  |
| DELETE | `/:id`   | Delete promotion              | Admin  |

### Discussions Routes

**Base URL:** `/api/v2/discussions`

| Method | Endpoint             | Description             | Access        |
| ------ | -------------------- | ----------------------- | ------------- |
| GET    | `/lessons/:lessonId` | Get lesson discussions  | Public        |
| POST   | `/`                  | Create discussion/reply | Authenticated |
| PUT    | `/:id`               | Update discussion       | Owner         |
| DELETE | `/:id`               | Delete discussion       | Owner         |
| POST   | `/vote`              | Vote on discussion      | Authenticated |

## Controller Structure

### Separated Controllers

1. **lessons.controller.js** - Regular news article lessons
2. **quizzes.controller.js** - All quiz types (QuizOne, QuizTwo, QuizThree)
3. **gre-lessons.controller.js** - GRE lessons and their quizzes
4. **exam-lessons.controller.js** - Exam lessons and their quizzes
5. **promotions.controller.js** - Promotional content management
6. **discussions.controller.js** - Lesson discussions and voting

### Benefits of New Structure

- **Single Responsibility:** Each controller handles one specific domain
- **RESTful URLs:** Follow standard REST conventions
- **Consistent Validation:** All endpoints use Joi validation
- **Proper HTTP Methods:** GET, POST, PUT, DELETE used appropriately
- **Nested Resources:** Related resources are properly nested (e.g., `/lessons/:id/quizzes`)
- **Comprehensive Documentation:** Each route is documented with JSDoc comments

## Migration Notes

### Breaking Changes

- Route paths have changed to follow REST conventions
- Some endpoints have been renamed for clarity
- Query parameters are now properly validated
- Response formats are more consistent

### Legacy Support

- Old routes are still available but marked as deprecated
- Gradual migration recommended
- New routes should be used for all new development

## Security Improvements

- All routes have proper authentication/authorization
- Input validation on all endpoints
- Rate limiting on sensitive operations
- File upload security for promotions
- Proper error handling and logging

## Migration Guide

### Old vs New Route Mapping

| Old Route                                     | New Route                         | Notes            |
| --------------------------------------------- | --------------------------------- | ---------------- |
| `POST /api/v2/auth/`                          | `POST /api/v2/auth/login`         | More explicit    |
| `POST /api/v2/auth/sendOtp`                   | `POST /api/v2/auth/otp/send`      | Nested resource  |
| `POST /api/v2/auth/verifyOtp`                 | `POST /api/v2/auth/otp/verify`    | Nested resource  |
| `POST /api/v2/auth/newAccessToken`            | `POST /api/v2/auth/token/refresh` | More descriptive |
| `POST /api/v2/register/`                      | `POST /api/v2/users/register`     | Clearer resource |
| `GET /api/v2/register/userExist`              | `GET /api/v2/users/exists`        | Better naming    |
| `POST /api/v2/lesson/addSingleLesson`         | `POST /api/v2/lessons/`           | RESTful          |
| `GET /api/v2/lesson/getAllLesson`             | `GET /api/v2/lessons/`            | RESTful          |
| `GET /api/v2/lesson/getLessonDetailsData/:id` | `GET /api/v2/lessons/:id`         | Simplified       |

### Response Format Changes

**Old Format:**

```json
{
  "message": "Success",
  "data": {...}
}
```

**New Format:**

```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": {...},
  "pagination": {...} // When applicable
}
```

### Error Response Format

**Consistent Error Format:**

```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "fieldName",
      "message": "Specific error message",
      "value": "invalid_value"
    }
  ]
}
```

## Development Guidelines

### Adding New Endpoints

1. Follow RESTful conventions
2. Use appropriate HTTP methods
3. Add Joi validation schemas
4. Include JSDoc comments
5. Add proper authentication/authorization
6. Handle errors consistently
7. Add to this documentation

### File Naming Conventions

- Controllers: `resource.controller.js`
- Routes: `resource.routes.js`
- Validators: `resource.validators.js`
- Services: `resource.service.js` (when needed)

### Code Organization

```
controller/v2/
├── lessons.controller.js
├── quizzes.controller.js
├── gre-lessons.controller.js
├── exam-lessons.controller.js
├── promotions.controller.js
└── discussions.controller.js

routes/v2/
├── lessons.routes.js
├── quizzes.routes.js
├── gre-lessons.routes.js
├── exam-lessons.routes.js
├── promotions.routes.js
└── discussions.routes.js
```
