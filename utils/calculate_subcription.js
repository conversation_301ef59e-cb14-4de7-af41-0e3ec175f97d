const calculateSubscription = (createdAt, duration) => {
    const currentDate = new Date(); // Correct way to get current date
    const createdDate = new Date(createdAt); // Convert createdAt string to Date object
    const expirationDate = new Date(createdDate);
    expirationDate.setMonth(createdDate.getMonth() + duration);

    return expirationDate > currentDate; // Check if expired
};

module.exports = { calculateSubscription };
