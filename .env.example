# Environment variables declared in this file are automatically made available to <PERSON>risma.
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL="mysql://username:password@localhost:3306/database_name"

# Server Configuration
PORT=5000
NODE_ENV=development

# JWT Configuration - CHANGE THESE IN PRODUCTION!
ACCESS_TOKEN_SECRET=your_super_secure_access_token_secret_here_change_this_in_production
REFRESH_TOKEN_SECRET=your_super_secure_refresh_token_secret_here_change_this_in_production

# JWT Token Expiry (recommended values)
ACCESS_TOKEN_EXPIRY=15m
REFRESH_TOKEN_EXPIRY=7d

# SMS API Configuration
SMS_API_KEY=your_sms_api_key_here
SMS_SENDER_ID=your_sms_sender_id_here
SMS_API_URL=http://bulksmsbd.net/api/smsapi

# Test Configuration (for development only - remove in production)
TEST_PHONE_NUMBER=01858573112
TEST_OTP=1234

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=5

# File Upload Configuration
MAX_FILE_SIZE_MB=5
ALLOWED_AUDIO_TYPES=audio/mpeg,audio/mp3,audio/wav,audio/ogg,audio/mp4
