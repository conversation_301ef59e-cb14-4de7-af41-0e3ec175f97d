const express = require("express");
const { getBooksList,addSingleBook,getSingleBook,updateBook,deleteBook,getReading<PERSON>ists,addReadingList,getSingleReadingList,updateReadingList,
    deleteReadingList,getReadingPassages,addReadingPassage,getSingleReadingPassage,updateReadingPassage,deleteReadingPassage,addQuizOne,getQuizzesOne,updateQuizOne,
    deleteQuizOne,getQuizzesTwo,addQuizTwo,getSingleQuizTwo,updateQuizTwo,deleteQuizTwo ,addPassage,deletePassage} = require("../controller/ielts_module_controller");
const router = express.Router();


router.get("/books", getBooksList);
router.post("/books", addSingleBook);
router.get("/books/:id", getSingleBook);
router.put("/books/:id", updateBook);
router.delete("/books/:id", deleteBook);


router.get("/readingLists", getReadingLists);
router.post("/readingLists", addReadingList);
router.get("/readingLists/:id", getSingleReadingList);
router.put("/readingLists/:id", updateReadingList);
router.delete("/readingLists/:id", deleteReadingList);


router.get("/readingPassages", getReadingPassages);
router.post("/readingPassages", addReadingPassage);
router.get("/readingPassages/:id", getSingleReadingPassage);
router.put("/readingPassages/:id", updateReadingPassage);
router.delete("/readingPassages/:id", deleteReadingPassage);


router.get("/quizzesOne", getQuizzesOne);
router.post("/quizzesOne", addQuizOne);
router.put("/quizzesOne/:id", updateQuizOne);
router.delete("/quizzesOne/:id", deleteQuizOne);


router.get("/quizzesTwo", getQuizzesTwo);
router.post("/quizzesTwo", addQuizTwo);
router.get("/quizzesTwo/:id", getSingleQuizTwo);
router.put("/quizzesTwo/:id", updateQuizTwo);
router.delete("/quizzesTwo/:id", deleteQuizTwo);

router.post("/passage", addPassage);
router.delete("/passage/:id", deletePassage);


module.exports = router;