const request = require('supertest');
const app = require('../../app');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

describe('Authentication Integration Tests', () => {
  let testUser;

  beforeAll(async () => {
    // Create a test user
    const hashedPassword = await bcrypt.hash('testpassword123', 12);
    testUser = await prisma.user.create({
      data: {
        name: 'Test User',
        phone: '01712345678',
        password: hashedPassword,
        role: {
          create: {
            role: 'USER',
          },
        },
      },
    });
  });

  afterAll(async () => {
    // Clean up test data
    await prisma.userRole.deleteMany({
      where: { userId: testUser.id },
    });
    await prisma.user.delete({
      where: { id: testUser.id },
    });
    await prisma.$disconnect();
  });

  describe('POST /api/v2/auth/login', () => {
    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/api/v2/auth/login')
        .send({
          phone: '01712345678',
          password: 'testpassword123',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.accessToken).toBeDefined();
      expect(response.body.refreshToken).toBeDefined();
      expect(response.body.user).toMatchObject({
        id: testUser.id,
        name: 'Test User',
        phone: '01712345678',
        roles: ['USER'],
      });
    });

    it('should reject invalid credentials', async () => {
      const response = await request(app)
        .post('/api/v2/auth/login')
        .send({
          phone: '01712345678',
          password: 'wrongpassword',
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid credentials');
    });

    it('should validate input format', async () => {
      const response = await request(app)
        .post('/api/v2/auth/login')
        .send({
          phone: 'invalid-phone',
          password: 'test',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should enforce rate limiting', async () => {
      // Make multiple requests to trigger rate limiting
      const promises = Array(10).fill().map(() =>
        request(app)
          .post('/api/v2/auth/login')
          .send({
            phone: '01712345678',
            password: 'wrongpassword',
          })
      );

      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('POST /api/v2/auth/otp/send', () => {
    it('should send OTP for valid phone number', async () => {
      const response = await request(app)
        .post('/api/v2/auth/otp/send')
        .send({
          phone: '01712345678',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBeDefined();
    });

    it('should validate phone number format', async () => {
      const response = await request(app)
        .post('/api/v2/auth/otp/send')
        .send({
          phone: 'invalid-phone',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });

    it('should enforce OTP rate limiting', async () => {
      // Make multiple OTP requests to trigger rate limiting
      const promises = Array(5).fill().map(() =>
        request(app)
          .post('/api/v2/auth/otp/send')
          .send({
            phone: '01712345679', // Different phone to avoid conflicts
          })
      );

      const responses = await Promise.all(promises);
      
      // Some requests should be rate limited
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('POST /api/v2/auth/otp/verify', () => {
    let hashedOtp;

    beforeAll(async () => {
      // Create a test OTP
      hashedOtp = await bcrypt.hash('1234', 12);
    });

    it('should verify valid OTP', async () => {
      const response = await request(app)
        .post('/api/v2/auth/otp/verify')
        .send({
          phone: '01712345678',
          enteredOtp: '1234',
          hashedOtp: hashedOtp,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.verified).toBe(true);
    });

    it('should reject invalid OTP', async () => {
      const response = await request(app)
        .post('/api/v2/auth/otp/verify')
        .send({
          phone: '01712345678',
          enteredOtp: '5678',
          hashedOtp: hashedOtp,
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.verified).toBe(false);
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v2/auth/otp/verify')
        .send({
          phone: '01712345678',
          // Missing enteredOtp and hashedOtp
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.errors).toBeDefined();
    });
  });

  describe('GET /api/v2/auth/health', () => {
    it('should return server health status', async () => {
      const response = await request(app)
        .get('/api/v2/auth/health');

      expect(response.status).toBe(200);
      // Add specific health check assertions based on your implementation
    });
  });
});
