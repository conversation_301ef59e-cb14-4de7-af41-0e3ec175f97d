const logger = require("../utils/logger");
const { errorTracker } = require("./performanceMonitor");
const config = require("../config/environment");

const errorHandler = (err, req, res, next) => {
  // Track error for monitoring
  errorTracker.trackError(err, {
    url: req.url,
    method: req.method,
    userId: req.user || null,
    requestId: req.requestId,
  });

  // Log the error with context
  logger.logError(err, req, {
    requestId: req.requestId,
    timestamp: new Date().toISOString(),
  });

  // Determine error response based on error type and environment
  let statusCode = 500;
  let message = "Internal Server Error";
  let errorCode = "INTERNAL_ERROR";

  // Handle specific error types
  if (err.name === "ValidationError") {
    statusCode = 400;
    message = "Validation Error";
    errorCode = "VALIDATION_ERROR";
  } else if (
    err.name === "UnauthorizedError" ||
    err.message.includes("unauthorized")
  ) {
    statusCode = 401;
    message = "Unauthorized";
    errorCode = "UNAUTHORIZED";
  } else if (
    err.name === "ForbiddenError" ||
    err.message.includes("forbidden")
  ) {
    statusCode = 403;
    message = "Forbidden";
    errorCode = "FORBIDDEN";
  } else if (
    err.name === "NotFoundError" ||
    err.message.includes("not found")
  ) {
    statusCode = 404;
    message = "Not Found";
    errorCode = "NOT_FOUND";
  } else if (err.name === "ConflictError" || err.message.includes("conflict")) {
    statusCode = 409;
    message = "Conflict";
    errorCode = "CONFLICT";
  } else if (err.name === "TooManyRequestsError") {
    statusCode = 429;
    message = "Too Many Requests";
    errorCode = "RATE_LIMIT_EXCEEDED";
  }

  // Prepare error response
  const errorResponse = {
    success: false,
    error: {
      code: errorCode,
      message: message,
      timestamp: new Date().toISOString(),
      requestId: req.requestId,
    },
  };

  // Include error details in development mode
  if (config.nodeEnv === "development") {
    errorResponse.error.details = {
      name: err.name,
      message: err.message,
      stack: err.stack,
    };
  }

  // Set appropriate headers
  res.status(statusCode);

  // Send error response
  res.json(errorResponse);
};

module.exports = errorHandler;
