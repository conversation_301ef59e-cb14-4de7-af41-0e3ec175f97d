const prisma = require("../../prisma/index");

/**
 * Exam Lessons Controller
 * Handles CRUD operations for exam lessons and their quizzes
 */

// Exam Lessons
const createExamLesson = async (req, res) => {
  try {
    const { title, content, lessonNumber } = req.body;

    const lesson = await prisma.examLesson.create({
      data: {
        title,
        content,
        lessonNumber: parseInt(lessonNumber),
      },
    });

    res.status(201).json({
      success: true,
      message: "Exam lesson created successfully",
      data: lesson,
    });
  } catch (error) {
    console.error("Error creating exam lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create exam lesson",
      error: error.message,
    });
  }
};

const getExamLessons = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const [lessons, totalCount] = await Promise.all([
      prisma.examLesson.findMany({
        include: {
          ExamQuizes: true,
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limitNum,
      }),
      prisma.examLesson.count(),
    ]);

    res.status(200).json({
      success: true,
      data: lessons,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
      },
    });
  } catch (error) {
    console.error("Error fetching exam lessons:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch exam lessons",
      error: error.message,
    });
  }
};

const getExamLessonById = async (req, res) => {
  try {
    const { id } = req.params;

    const lesson = await prisma.examLesson.findUnique({
      where: { id },
      include: {
        ExamQuizes: true,
      },
    });

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: "Exam lesson not found",
      });
    }

    res.status(200).json({
      success: true,
      data: lesson,
    });
  } catch (error) {
    console.error("Error fetching exam lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch exam lesson",
      error: error.message,
    });
  }
};

const updateExamLesson = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, lessonNumber } = req.body;

    const lesson = await prisma.examLesson.update({
      where: { id },
      data: {
        title,
        content,
        lessonNumber: parseInt(lessonNumber),
      },
    });

    res.status(200).json({
      success: true,
      message: "Exam lesson updated successfully",
      data: lesson,
    });
  } catch (error) {
    console.error("Error updating exam lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update exam lesson",
      error: error.message,
    });
  }
};

const deleteExamLesson = async (req, res) => {
  try {
    const { id } = req.params;

    // First delete all related quizzes
    await prisma.examQuizes.deleteMany({
      where: { lessonId: id },
    });

    // Then delete the lesson
    await prisma.examLesson.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Exam lesson and related quizzes deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting exam lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete exam lesson",
      error: error.message,
    });
  }
};

// Exam Quizzes
const createExamQuiz = async (req, res) => {
  try {
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    const quiz = await prisma.examQuizes.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId,
      },
    });

    res.status(201).json({
      success: true,
      message: "Exam quiz created successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error creating exam quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create exam quiz",
      error: error.message,
    });
  }
};

const getExamQuizzesByLessonId = async (req, res) => {
  try {
    const { lessonId } = req.params;

    const quizzes = await prisma.examQuizes.findMany({
      where: { lessonId },
    });

    res.status(200).json({
      success: true,
      data: quizzes,
    });
  } catch (error) {
    console.error("Error fetching exam quizzes:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch exam quizzes",
      error: error.message,
    });
  }
};

const updateExamQuiz = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, answer } = req.body;

    const quiz = await prisma.examQuizes.update({
      where: { id },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
      },
    });

    res.status(200).json({
      success: true,
      message: "Exam quiz updated successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error updating exam quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update exam quiz",
      error: error.message,
    });
  }
};

const deleteExamQuiz = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.examQuizes.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Exam quiz deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting exam quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete exam quiz",
      error: error.message,
    });
  }
};

module.exports = {
  // Exam Lessons
  createExamLesson,
  getExamLessons,
  getExamLessonById,
  updateExamLesson,
  deleteExamLesson,
  
  // Exam Quizzes
  createExamQuiz,
  getExamQuizzesByLessonId,
  updateExamQuiz,
  deleteExamQuiz,
};
