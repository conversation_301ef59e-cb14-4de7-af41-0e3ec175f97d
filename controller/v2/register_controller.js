const prisma = require("../../prisma/index");
const bcrypt = require("bcrypt");


const checkUserExist = async (req, res) => {
    const { phone } = req.query;

    try {
        const user = await prisma.user.findUnique({
            where: {
                phone: phone
            }
        });

        if (!user) {
            res.status(200).json({ messsage: false });
        }
        else {
            res.status(200).json({ messsage: true });
        }
    }
    catch (err) {

    }
};


const registerNewUser = async (req, res) => {
    const { name, phone, password } = req.body;

    if (!name || !phone || !password) {
        return res
            .status(400)
            .json({ message: "Valid credentials are not provided" });
    }

    const duplicate = await prisma.user.findFirst({
        where: {
            phone: phone
        },
    });

    if (duplicate) {
        return res
            .status(409)
            .json({ message: "User with same email or phone number exists" });
    }

    try {
        const hashedPassword = await bcrypt.hash(password, 10);

        const newUser = await prisma.user.create({
            data: {
                name: name,
                phone: phone,
                password: hashedPassword,
              
                role: {
                    create: {
                        role: "USER",
                    },
                },
            },
        });

        res.status(201).json({
            message: `Registration Complete`,
        });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: error.message });
    }
};



const changeUserPassword = async (req, res) => {
    const { phone, newPassword } = req.body;

    // Check if all required fields are provided
    if (!phone || !newPassword) {
        return res.status(400).json({ message: "All fields are required" });
    }

    try {
        // Find the user by phone number
        const user = await prisma.user.findUnique({
            where: { phone: phone },
        });

        // If user not found
        if (!user) {
            return res.status(404).json({ message: "User not found" });
        }

        // Hash the new password
        const hashedNewPassword = await bcrypt.hash(newPassword, 10);

        // Update the user's password in the database
        await prisma.user.update({
            where: { phone: phone },
            data: { password: hashedNewPassword },
        });

        // Respond with success message
        res.status(200).json({ message: "Password changed successfully" });
    } catch (error) {
        console.error(error);
        res.status(500).json({ message: "Internal Server Error" });
    }
};



module.exports = {
    registerNewUser, checkUserExist,changeUserPassword
};