const express = require("express");
const {
  handleLogin,
  sendOtp,
  verifyOtp,
  testServer,
  newAccessToken,
  handleLoginAdmin,
} = require("../../controller/v2/authentication_controller");
const { validateBody } = require("../../middleware/validateRequest");
const { authLimiter, otpLimiter } = require("../../middleware/rateLimiter");
const {
  loginSchema,
  otpRequestSchema,
  otpVerificationSchema,
  refreshTokenSchema,
} = require("../../validators/authValidators");

const router = express.Router();

// Test endpoint (no rate limiting for development)
router.get("/testServer", testServer);

// Login endpoints with rate limiting and validation
router.post("/", authLimiter, validateBody(loginSchema), handleLogin);
router.post("/admin", authLimiter, validateBody(loginSchema), handleLoginAdmin);

// OTP endpoints with strict rate limiting and validation
router.post("/sendOtp", otpLimiter, validateBody(otpRequestSchema), sendOtp);
router.post(
  "/verifyOtp",
  authLimiter,
  validateBody(otpVerificationSchema),
  verifyOtp
);

// Token refresh endpoint with validation
router.post(
  "/newAccessToken",
  validateBody(refreshTokenSchema),
  newAccessToken
);

module.exports = router;
