{"environment":"development","level":"info","message":"Server started on port 5000","port":"5000","timestamp":"2025-06-26 14:46:55:4655"}
{"duration":"4.595916ms","level":"info","memoryDelta":{"heapTotal":0.25,"heapUsed":0.26},"message":"Performance Metric","method":"GET","operation":"HTTP_REQUEST","statusCode":200,"timestamp":"2025-06-26 14:49:18:4918","url":"/health"}
{"ip":"::1","level":"http","message":"HTTP Request","method":"GET","responseTime":"6ms","statusCode":200,"timestamp":"2025-06-26 14:49:18:4918","url":"/health","userAgent":"curl/8.7.1","userId":null}
{"level":"http","message":"GET /api/v2/auth/health 200 6.378 ms - 18 bytes","timestamp":"2025-06-26 14:49:18:4918"}
{"environment":"development","level":"info","message":"Server started on port 5000","port":"5000","timestamp":"2025-06-26 14:50:09:509"}
{"environment":"development","level":"info","message":"Server started on port 5000","port":"5000","timestamp":"2025-06-26 14:50:52:5052"}
{"environment":"development","level":"info","message":"Server started on port 5000","port":"5000","timestamp":"2025-06-26 14:51:44:5144"}
