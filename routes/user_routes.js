const express = require("express");
const router = express.Router();
const { getUserInfo,updateOrCreateScore,getAllScoresWithUsers,getSubscriptionRequests,addSubscription,getAllUser,getUserScore,getAdminDashBoardStats,deleteSubscription } = require("../controller/user_controller");

router.get("/", getUserInfo);
router.post("/updateScoreById", updateOrCreateScore);
router.post("/getScoreList", getAllScoresWithUsers);
router.post("/getSubscriptionRequests", getSubscriptionRequests);
router.post("/addSubscription", addSubscription);
router.post("/getAllUser", getAllUser);
router.post("/getUserScore", getUserScore);
router.get("/getAdminDashBoardStats", getAdminDashBoardStats);
router.post("/deleteSubscription", deleteSubscription);
module.exports = router;