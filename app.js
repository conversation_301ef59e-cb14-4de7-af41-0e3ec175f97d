const express = require("express");
const app = express();
const path = require("path");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const fileUpload = require("express-fileupload");
const helmet = require("helmet");

// Import configuration
const config = require("./config/environment");

// Import middleware
const { logger } = require("./middleware/logEvents");
const { credentials } = require("./middleware/credentials");
const errorHandler = require("./middleware/errorHandler");
const { generalLimiter } = require("./middleware/rateLimiter");

const corsOptions = require("./config/corsOptions");

const PORT = config.port;

//v1 routes
const authenticationRoutes = require("./routes/authentication_routes");
const registerRouter = require("./routes/register_routes");
const lessonRouter = require("./routes/lesson_routes");
const userRouter = require("./routes/user_routes");
const ieltsRouter = require("./routes/ielts_module_routes");

//v2 routes
const authenticationRoutes2 = require("./routes/v2/authentication_routes");
const registerRouter2 = require("./routes/v2/register_routes");
const lessonRouter2 = require("./routes/v2/lesson_routes");
const userRouter2 = require("./routes/v2/user_routes");
const ieltsRouter2 = require("./routes/v2/ielts_module_routes");

// Security middlewares (should be first)
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  })
);

// Rate limiting (apply to all requests)
app.use(generalLimiter);

// Built-in middlewares
app.use(express.urlencoded({ extended: false, limit: "10mb" }));
app.use(express.json({ limit: "10mb" }));
app.use(express.static(path.join(__dirname, "public")));
app.use(express.static("public"));
app.use(
  fileUpload({
    createParentPath: true,
    limits: { fileSize: config.fileUpload.maxSizeMB * 1024 * 1024 },
    abortOnLimit: true,
  })
);
app.use(cookieParser());
app.use(
  "/public/files",
  express.static(path.join(__dirname, "public", "files"))
);

// Custom middlewares
app.use(logger);
app.use(credentials);

// Third-party middlewares
app.use(cors(corsOptions));

// Route handlers
app.use("/auth", authenticationRoutes);
app.use("/register", registerRouter);
app.use("/lesson", lessonRouter);
app.use("/user", userRouter);
app.use("/ielts", ieltsRouter);

app.use("/api/v2/auth", authenticationRoutes2);
app.use("/api/v2/register", registerRouter2);
app.use("/api/v2/lesson", lessonRouter2);
app.use("/api/v2/user", userRouter2);
app.use("/api/v2/ielts", ieltsRouter2);

app.get("/", (req, res) => {
  res.send("Hello from the Server-side!");
});

app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Invalid Api route",
  });
});
// Error handling middleware
app.use(errorHandler);

// Start the server
app.listen(PORT, () => {
  console.log(`Server initiated at http://localhost:${PORT}`);
});
