const multer = require("multer");
const path = require("path");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");

// Ensure upload directory exists
const createUploadDir = () => {
  const uploadDir = path.join(process.cwd(), "public/audio");
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
  }
  return uploadDir;
};

// Configure multer storage
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const uploadDir = createUploadDir();
    cb(null, uploadDir);
  },
  filename: function (req, file, cb) {
    const uniqueFileName = `${uuidv4()}_${file.originalname}`;
    cb(null, uniqueFileName);
  }
});

// File filter to accept only audio files
const fileFilter = (req, file, cb) => {
  const allowedMimeTypes = [
    'audio/mpeg',
    'audio/mp3',
    'audio/wav',
    'audio/ogg',
    'audio/mp4'
  ];
  
  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('Invalid file type. Only audio files are allowed.'), false);
  }
};

// Create multer upload instance
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB limit
  }
});

// Function to handle single audio upload
const uploadAudio = upload.single('audio');

// Function to get the file path from a request
const getAudioPath = (req) => {
  if (!req.file) return null;
  
  return `/public/audio/${req.file.filename}`;
};

// Function to remove an audio file
const removeAudio = (audioPath) => {
  if (!audioPath) return;
  
  const fullPath = path.join(process.cwd(), audioPath.replace(/^\/public/, 'public'));
  
  if (fs.existsSync(fullPath)) {
    fs.unlinkSync(fullPath);
  }
};

module.exports = {
  uploadAudio,
  getAudioPath,
  removeAudio
};