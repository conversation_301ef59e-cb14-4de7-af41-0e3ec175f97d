const logger = require('../utils/logger');
const config = require('../config/environment');

// Performance thresholds (in milliseconds)
const PERFORMANCE_THRESHOLDS = {
  SLOW_REQUEST: 1000,      // 1 second
  VERY_SLOW_REQUEST: 3000, // 3 seconds
  SLOW_DB_QUERY: 500,      // 500ms
  VERY_SLOW_DB_QUERY: 1000, // 1 second
};

// Memory usage monitoring
const memoryMonitor = {
  checkMemoryUsage: () => {
    const usage = process.memoryUsage();
    const formatBytes = (bytes) => Math.round(bytes / 1024 / 1024 * 100) / 100;
    
    return {
      rss: formatBytes(usage.rss),           // Resident Set Size
      heapTotal: formatBytes(usage.heapTotal), // Total heap size
      heapUsed: formatBytes(usage.heapUsed),   // Used heap size
      external: formatBytes(usage.external),   // External memory
      arrayBuffers: formatBytes(usage.arrayBuffers || 0),
    };
  },
  
  logMemoryUsage: () => {
    const usage = memoryMonitor.checkMemoryUsage();
    logger.info('Memory Usage', usage);
    
    // Alert if memory usage is high
    if (usage.heapUsed > 500) { // 500MB threshold
      logger.warn('High Memory Usage Detected', {
        heapUsed: `${usage.heapUsed}MB`,
        heapTotal: `${usage.heapTotal}MB`,
        percentage: Math.round((usage.heapUsed / usage.heapTotal) * 100),
      });
    }
  },
};

// CPU usage monitoring (simplified)
const cpuMonitor = {
  startTime: process.hrtime(),
  
  getCpuUsage: () => {
    const usage = process.cpuUsage();
    const elapsed = process.hrtime(cpuMonitor.startTime);
    const elapsedMicros = elapsed[0] * 1e6 + elapsed[1] / 1e3;
    
    return {
      user: Math.round((usage.user / elapsedMicros) * 100 * 100) / 100,
      system: Math.round((usage.system / elapsedMicros) * 100 * 100) / 100,
    };
  },
};

// Request performance monitoring middleware
const requestPerformanceMonitor = (req, res, next) => {
  const startTime = process.hrtime.bigint();
  const startMemory = process.memoryUsage();
  
  // Override res.end to capture metrics
  const originalEnd = res.end;
  res.end = function(chunk, encoding) {
    const endTime = process.hrtime.bigint();
    const duration = Number(endTime - startTime) / 1e6; // Convert to milliseconds
    const endMemory = process.memoryUsage();
    
    // Calculate memory delta
    const memoryDelta = {
      heapUsed: endMemory.heapUsed - startMemory.heapUsed,
      heapTotal: endMemory.heapTotal - startMemory.heapTotal,
    };
    
    // Log performance metrics
    const performanceData = {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      duration: Math.round(duration * 100) / 100,
      memoryDelta: {
        heapUsed: Math.round(memoryDelta.heapUsed / 1024 / 1024 * 100) / 100,
        heapTotal: Math.round(memoryDelta.heapTotal / 1024 / 1024 * 100) / 100,
      },
      requestId: req.requestId,
    };
    
    // Log based on performance thresholds
    if (duration > PERFORMANCE_THRESHOLDS.VERY_SLOW_REQUEST) {
      logger.warn('Very Slow Request', performanceData);
    } else if (duration > PERFORMANCE_THRESHOLDS.SLOW_REQUEST) {
      logger.warn('Slow Request', performanceData);
    } else {
      logger.debug('Request Performance', performanceData);
    }
    
    // Log performance metric
    logger.logPerformance('HTTP_REQUEST', duration, {
      method: req.method,
      url: req.url,
      statusCode: res.statusCode,
      memoryDelta: performanceData.memoryDelta,
    });
    
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};

// Database query performance monitor
const dbQueryMonitor = (operation, table) => {
  const startTime = process.hrtime.bigint();
  
  return {
    end: (success = true, error = null) => {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1e6; // Convert to milliseconds
      
      // Log database operation
      logger.logDatabase(operation, table, Math.round(duration * 100) / 100, success, error);
      
      // Alert on slow queries
      if (duration > PERFORMANCE_THRESHOLDS.VERY_SLOW_DB_QUERY) {
        logger.warn('Very Slow Database Query', {
          operation,
          table,
          duration: `${Math.round(duration * 100) / 100}ms`,
          success,
          error: error?.message,
        });
      } else if (duration > PERFORMANCE_THRESHOLDS.SLOW_DB_QUERY) {
        logger.warn('Slow Database Query', {
          operation,
          table,
          duration: `${Math.round(duration * 100) / 100}ms`,
          success,
        });
      }
      
      return duration;
    },
  };
};

// System health monitoring
const healthMonitor = {
  getSystemHealth: () => {
    const memory = memoryMonitor.checkMemoryUsage();
    const cpu = cpuMonitor.getCpuUsage();
    const uptime = process.uptime();
    
    return {
      status: 'healthy', // This could be calculated based on thresholds
      uptime: Math.round(uptime),
      memory,
      cpu,
      timestamp: new Date().toISOString(),
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch,
    };
  },
  
  logSystemHealth: () => {
    const health = healthMonitor.getSystemHealth();
    logger.info('System Health Check', health);
    return health;
  },
};

// Periodic monitoring setup
const startPeriodicMonitoring = () => {
  if (config.nodeEnv === 'production') {
    // Log system health every 5 minutes
    setInterval(() => {
      healthMonitor.logSystemHealth();
    }, 5 * 60 * 1000);
    
    // Log memory usage every minute
    setInterval(() => {
      memoryMonitor.logMemoryUsage();
    }, 60 * 1000);
  }
};

// Error tracking and alerting
const errorTracker = {
  errorCounts: new Map(),
  
  trackError: (error, context = {}) => {
    const errorKey = `${error.name}:${error.message}`;
    const count = this.errorCounts.get(errorKey) || 0;
    this.errorCounts.set(errorKey, count + 1);
    
    // Alert on repeated errors
    if (count > 5) { // More than 5 occurrences
      logger.error('Repeated Error Alert', {
        error: errorKey,
        count: count + 1,
        context,
        timestamp: new Date().toISOString(),
      });
    }
  },
  
  getErrorStats: () => {
    const stats = {};
    for (const [error, count] of this.errorCounts.entries()) {
      stats[error] = count;
    }
    return stats;
  },
  
  resetErrorCounts: () => {
    this.errorCounts.clear();
  },
};

// Reset error counts every hour
setInterval(() => {
  errorTracker.resetErrorCounts();
}, 60 * 60 * 1000);

module.exports = {
  requestPerformanceMonitor,
  dbQueryMonitor,
  memoryMonitor,
  cpuMonitor,
  healthMonitor,
  errorTracker,
  startPeriodicMonitoring,
  PERFORMANCE_THRESHOLDS,
};
