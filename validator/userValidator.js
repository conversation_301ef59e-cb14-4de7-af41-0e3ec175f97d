const Joi = require('joi');

// Phone number validation (Bangladesh format)
const phoneSchema = Joi.string()
  .pattern(/^01[3-9]\d{8}$/)
  .required()
  .messages({
    'string.pattern.base': 'Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)',
    'any.required': 'Phone number is required'
  });

// Password validation
const passwordSchema = Joi.string()
  .min(6)
  .max(128)
  .required()
  .messages({
    'string.min': 'Password must be at least 6 characters long',
    'string.max': 'Password must not exceed 128 characters',
    'any.required': 'Password is required'
  });

// Name validation
const nameSchema = Joi.string()
  .min(2)
  .max(100)
  .trim()
  .required()
  .messages({
    'string.min': 'Name must be at least 2 characters long',
    'string.max': 'Name must not exceed 100 characters',
    'any.required': 'Name is required'
  });

// User creation schema
const createUserSchema = Joi.object({
  name: nameSchema,
  phone: phoneSchema,
  password: passwordSchema
});

module.exports = createUserSchema;
