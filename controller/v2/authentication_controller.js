const prisma = require("../../prisma/index");
const { sendSms } = require("../../utils/sendOtp");
const bcrypt = require("bcrypt");
require("dotenv").config();
const jwt = require("jsonwebtoken");

const testServer = async (req, res) => {
return res.status(200).json({ message: 'okay' });
};


const handleLogin = async (req, res) => {
    const { phone, password, token } = req.body;

    if (!phone || !password) {
        return res
            .status(400)
            .json({ message: "Phone number and Password required!!" });
    }

    const user = await prisma.user.findUnique({
        where: {
            phone: phone,
        },
        include: {

            role: true,
        },
    });

    if (!user) {
        return res.status(400).json({ message: "No user exist" });
    }

    const match = await bcrypt.compare(password, user.password);

    if (match) {
        // JWT

        const roles = Object.values(user.role).map((role) => {
            return role.role;
        });

        const accessToken = jwt.sign(
            { id: user.id, roles: roles },
            process.env.ACCESS_TOKEN_SECRET,
            { expiresIn: "1 min" }
        );

        const refreshToken = jwt.sign(
            { id: user.id },
            process.env.REFRESH_TOKEN_SECRET,
            { expiresIn: "1200d" }
        );

        await prisma.user.update({
            where: {
                id: user.id,
            },
            data: {
                refreshToken: refreshToken,
                token: token,
            },
        });

        res.cookie("jwt", accessToken, {
            httpOnly: true,
            sameSite: "None",
            secure: true,
            maxAge: 24 * 60 * 60 * 1000,
        });

        return res.json({ accessToken, refreshToken });
    } else {
        return res.status(401).json({ message: "Wrong Password!" });
    }
};

const handleLoginAdmin = async (req, res) => {
    const { phone, password, token } = req.body;

    if (!phone || !password) {
        return res
            .status(400)
            .json({ message: "Phone number and Password required!!" });
    }

    const user = await prisma.user.findUnique({
        where: {
            phone: phone,
        },
        include: {

            role: true,
        },
    });

    if (!user) {
        return res.status(400).json({ message: "No user exist" });
    }

    if (user.role[0].role !== "ADMIN") {
        return res.status(400).json({ message: "You are not an admin" });
    }

    const match = await bcrypt.compare(password, user.password);

    if (match) {
        // JWT

        const roles = Object.values(user.role).map((role) => {
            return role.role;
        });

        const accessToken = jwt.sign(
            { id: user.id, roles: roles },
            process.env.ACCESS_TOKEN_SECRET,
            { expiresIn: "1 min" }
        );

        const refreshToken = jwt.sign(
            { id: user.id },
            process.env.REFRESH_TOKEN_SECRET,
            { expiresIn: "1200d" }
        );

        await prisma.user.update({
            where: {
                id: user.id,
            },
            data: {
                refreshToken: refreshToken,
                token: token,
            },
        });

        res.cookie("jwt", accessToken, {
            httpOnly: true,
            sameSite: "None",
            secure: true,
            maxAge: 24 * 60 * 60 * 1000,
        });

        return res.json({ accessToken, refreshToken });
    } else {
        return res.status(401).json({ message: "Wrong Password!" });
    }
};


const sendOtp = async (req, res) => {
  const phone = req.query.phone;

  try {
      if(phone=='01858573112'){
            res.status(200).json({ message: `1234` });  
      }
      
    const num = Math.floor(1000 + Math.random() * 9000); // Generate OTP as a number
    const otpString = num.toString();// Convert OTP number to a string

    console.log(`${otpString}`);
    const otp = await bcrypt.hash(otpString, 10); // Hash the OTP string

    const apiKey = 'wyEftUHaMBMPS4DrUm2t';
    const senderId = '8809617617760';
    const phoneNumbers = `88${phone}`;
    const message = `Welcome to News Lexica, your OTP is ${num}`;
    sendSms(apiKey, senderId, phoneNumbers, message)
      .then((response) => {
        console.log('SMS sent successfully:', response);
        res.status(200).json({ message: `${otp}` });
      })
      .catch((error) => {
        console.error('Error sending SMS:', error.message);
        res.status(500).json({ error: 'Error sending SMS' });
      });

  } catch (err) {
    console.error('Error:', err);
    res.status(500).json({ error: `${err}` });
  }
}



const verifyOtp = async (req, res) => {
  const secretOtp = req.query.secretOtp;
  const enteredOtp = req.query.enteredOtp; // OTP entered by the user
  try {
    const comp = bcrypt.compareSync(enteredOtp, secretOtp);
    if (comp) {
      res.status(200).json({ message: true });
    }
    else {
      res.status(400).json({ message: 'Otp not matched' });
    }

  } catch (err) {
    console.error('Error verifying OTP:', err);
    res.status(500).json({ error: `${err}` });
  }
}


const newAccessToken=async(req,res)=>{
  const {refreshToken}=req.body;
  if(!refreshToken){
      return res.status(400).json({message:"Refresh token is required!"});
  }
  try{
      const user=await prisma.user.findUnique({
          where:{
              refreshToken:refreshToken
          },
          include: {

              role: true,
          },
      });
      if(!user){
          return res.status(404).json({message:"User not found!"});
      }

      
      const roles = Object.values(user.role).map((role) => {
          return role.role;
      });
      const accessToken=jwt.sign({id:user.id,roles:roles},process.env.ACCESS_TOKEN_SECRET,{expiresIn:"1 min"});
      const newRefreshToken=jwt.sign({id:user.id},process.env.REFRESH_TOKEN_SECRET,{expiresIn:"30d"});
      
      await prisma.user.update({
          where: {
              id: user.id,
          },
          data: {
              refreshToken: newRefreshToken,
            
          },
      });
      return res.json({accessToken,newRefreshToken});
  }
  catch(error){
      console.log(error);
      return res.status(500).json({message:"Internal server error!"});
  }
}




module.exports = { handleLogin, sendOtp, verifyOtp,testServer,newAccessToken,handleLoginAdmin };