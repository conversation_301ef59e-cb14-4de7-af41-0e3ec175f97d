const request = require('supertest');
const express = require('express');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');

// Mock Prisma
const mockPrisma = {
  user: {
    findUnique: jest.fn(),
    update: jest.fn(),
    create: jest.fn(),
  },
};

jest.mock('../../../prisma/index', () => mockPrisma);
jest.mock('../../../utils/sendOtp');

const { handleLogin, sendOtp, verifyOtp } = require('../../../controller/v2/authentication_controller');
const config = require('../../../config/environment');

describe('Authentication Controller', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    jest.clearAllMocks();
  });

  describe('handleLogin', () => {
    it('should login user with valid credentials', async () => {
      const mockUser = {
        id: 1,
        name: 'Test User',
        phone: '01712345678',
        password: await bcrypt.hash('password123', 12),
        role: [{ role: 'USER' }],
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);
      mockPrisma.user.update.mockResolvedValue(mockUser);

      app.post('/login', handleLogin);

      const response = await request(app)
        .post('/login')
        .send({
          phone: '01712345678',
          password: 'password123',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.accessToken).toBeDefined();
      expect(response.body.refreshToken).toBeDefined();
      expect(response.body.user).toEqual({
        id: 1,
        name: 'Test User',
        phone: '01712345678',
        roles: ['USER'],
      });
    });

    it('should return 401 for invalid credentials', async () => {
      mockPrisma.user.findUnique.mockResolvedValue(null);

      app.post('/login', handleLogin);

      const response = await request(app)
        .post('/login')
        .send({
          phone: '01712345678',
          password: 'wrongpassword',
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid credentials');
    });

    it('should return 400 for missing credentials', async () => {
      app.post('/login', handleLogin);

      const response = await request(app)
        .post('/login')
        .send({
          phone: '01712345678',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Phone number and password are required');
    });

    it('should return 401 for wrong password', async () => {
      const mockUser = {
        id: 1,
        name: 'Test User',
        phone: '01712345678',
        password: await bcrypt.hash('correctpassword', 12),
        role: [{ role: 'USER' }],
      };

      mockPrisma.user.findUnique.mockResolvedValue(mockUser);

      app.post('/login', handleLogin);

      const response = await request(app)
        .post('/login')
        .send({
          phone: '01712345678',
          password: 'wrongpassword',
        });

      expect(response.status).toBe(401);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Invalid credentials');
    });
  });

  describe('sendOtp', () => {
    it('should send OTP for valid phone number', async () => {
      app.post('/send-otp', sendOtp);

      const response = await request(app)
        .post('/send-otp')
        .send({
          phone: '01712345678',
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBeDefined();
    });

    it('should return test OTP in development mode', async () => {
      const originalEnv = config.nodeEnv;
      config.nodeEnv = 'development';

      app.post('/send-otp', sendOtp);

      const response = await request(app)
        .post('/send-otp')
        .send({
          phone: config.test.phoneNumber,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.isTest).toBe(true);

      config.nodeEnv = originalEnv;
    });

    it('should return 400 for missing phone number', async () => {
      app.post('/send-otp', sendOtp);

      const response = await request(app)
        .post('/send-otp')
        .send({});

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Phone number is required');
    });
  });

  describe('verifyOtp', () => {
    it('should verify valid OTP', async () => {
      const testOtp = '1234';
      const hashedOtp = await bcrypt.hash(testOtp, 12);

      app.post('/verify-otp', verifyOtp);

      const response = await request(app)
        .post('/verify-otp')
        .send({
          phone: '01712345678',
          enteredOtp: testOtp,
          hashedOtp: hashedOtp,
        });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.verified).toBe(true);
    });

    it('should reject invalid OTP', async () => {
      const testOtp = '1234';
      const hashedOtp = await bcrypt.hash(testOtp, 12);

      app.post('/verify-otp', verifyOtp);

      const response = await request(app)
        .post('/verify-otp')
        .send({
          phone: '01712345678',
          enteredOtp: '5678', // Wrong OTP
          hashedOtp: hashedOtp,
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
      expect(response.body.verified).toBe(false);
    });

    it('should return 400 for missing required fields', async () => {
      app.post('/verify-otp', verifyOtp);

      const response = await request(app)
        .post('/verify-otp')
        .send({
          phone: '01712345678',
        });

      expect(response.status).toBe(400);
      expect(response.body.success).toBe(false);
    });
  });
});
