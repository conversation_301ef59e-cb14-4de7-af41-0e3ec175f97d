const prisma = require("../../prisma/index");
const { uploadFiles, removeFile } = require("../../utils/uploadFiles");
const addSingleLesson = async (req, res) => {
  const {
    lessonTitle,
    title,
    lessonNumber,
    paragraph,
    part,
    pressName,
    publishDate,
    contentType,
    category,
  } = req.body;

  if (
    !lessonTitle ||
    !title ||
    !lessonNumber ||
    !paragraph ||
    !part ||
    !pressName ||
    !publishDate ||
    !category
  ) {
    return res.status(400).json({ message: "plase provide valid credentials" });
  }

  try {
    const lesson = await prisma.lesson.create({
      data: {
        lessonTitle: lessonTitle,
        lessonNumber: parseInt(lessonNumber),
        paragraph: paragraph,
        pressName: pressName,
        part: parseInt(part),
        publishDate: publishDate,
        title: title,
        contentType: parseInt(contentType),
        category: category,
      },
    });

    res
      .status(200)
      .json({ message: `New lesson ${lesson.lessonNumber} created` });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const updateLesson = async (req, res) => {
  const { id } = req.params; // Get lesson ID from URL params
  const {
    lessonTitle,
    title,
    lessonNumber,
    paragraph,
    part,
    pressName,
    publishDate,
    contentType,
    category,
  } = req.body;

  if (!id) {
    return res.status(400).json({ message: "Lesson ID is required" });
  }

  try {
    const existingLesson = await prisma.lesson.findUnique({
      where: { id: parseInt(id) },
    });

    if (!existingLesson) {
      return res.status(404).json({ message: "Lesson not found" });
    }

    const updatedLesson = await prisma.lesson.update({
      where: { id: parseInt(id) },
      data: {
        lessonTitle,
        lessonNumber: parseInt(lessonNumber),
        paragraph,
        pressName,
        part: parseInt(part),
        publishDate,
        title,
        contentType: parseInt(contentType),
        category,
      },
    });

    res
      .status(200)
      .json({ message: `Lesson ${updatedLesson.id} updated successfully` });
  } catch (err) {
    console.error(err);
    res
      .status(500)
      .json({ message: "Something went wrong", error: err.message });
  }
};

const getAllLesson = async (req, res) => {
  const { contentType, category, part } = req.query;
  let whereClause = {};

  if (contentType) whereClause.contentType = parseInt(contentType);
  if (part) whereClause.part = parseInt(part);
  if (category) whereClause.category = category;

  try {
    const lesson = await prisma.lesson.findMany({
      where: whereClause,
      select: {
        lessonNumber: true,
        category: true,
        title: true,
        pressName: true,
        id: true,
      },
    });
    res.status(200).json({ message: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const getAllLessonByPage = async (req, res) => {
  const { limit, page } = req.query;

  // Parse limit and page query parameters
  const limitValue = parseInt(limit) || 10; // Default limit to 10 if not provided
  const pageValue = parseInt(page) || 1; // Default page to 1 if not provided

  // Calculate the offset
  const offset = (pageValue - 1) * limitValue;

  try {
    const lessons = await prisma.lesson.findMany({
      skip: offset,
      take: limitValue,
    });

    // Get the total count of lessons for pagination info
    const totalLessons = await prisma.lesson.count();

    // Calculate total pages
    const totalPages = Math.ceil(totalLessons / limitValue);

    res.status(200).json({
      message: lessons,
      pagination: {
        totalLessons,
        totalPages,
        currentPage: pageValue,
        limit: limitValue,
      },
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getLessonDetailsData = async (req, res) => {
  const { id } = req.params;
  const lessonIdInt = parseInt(id);
  try {
    const lesson = await prisma.lesson.findUnique({
      where: {
        id: lessonIdInt,
      },
      include: {
        QuizOne: true,
        QuizTwo: true,
        QuizThree: true,
      },
    });
    res.status(200).json({ message: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const getExamQuizListByLesson = async (req, res) => {
  const { lessonNumber } = req.query;

  try {
    const lesson = await prisma.examQuizes.findMany({
      where: {
        lessonNumber: parseInt(lessonNumber),
      },
    });
    res.status(200).json({ message: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const getGreQuizListByLesson = async (req, res) => {
  const { lessonNumber } = req.query;

  try {
    const lesson = await prisma.greQuizes.findMany({
      where: {
        lessonNumber: parseInt(lessonNumber),
      },
    });
    res.status(200).json({ message: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const addPromotion = async (req, res) => {
  const { name } = req.body;
  const files = req.files;

  // Validate required fields
  if (!name) {
    return res
      .status(400)
      .json({ message: "All required fields must be provided" });
  }

  try {
    const fileNames = uploadFiles(files);
    // Create a new promotion
    const newPromotion = await prisma.promotion.create({
      data: {
        name,
        photo: fileNames.photo,
      },
    });

    // Respond with the newly created promotion
    res.status(201).json({ message: newPromotion });
  } catch (err) {
    console.error("Error adding promotion:", err);
    res.status(500).json({ message: "Internal server error" });
  }
};

const getAllPromotion = async (req, res) => {
  try {
    const promotion = await prisma.promotion.findMany({});
    res.status(200).json({ message: promotion });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const addQuizOne = async (req, res) => {
  const { question, option1, option2, option3, option4, answer, lessonId } =
    req.body;

  try {
    const newQuiz = await prisma.quizOne.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId: parseInt(lessonId), // Still need to parse lessonId as it's related to lessonNumber which is an Int
      },
    });
    res.status(201).json(newQuiz);
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const editQuizOne = async (req, res) => {
  const { id } = req.params;
  const { question, option1, option2, option3, option4, answer, lessonId } =
    req.body;

  try {
    const updatedQuizOne = await prisma.quizOne.update({
      where: { id: parseInt(id) },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
      },
    });
    res
      .status(200)
      .json({ message: "QuizOne updated successfully", data: updatedQuizOne });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteQuizOne = async (req, res) => {
  const { id } = req.params;

  try {
    await prisma.quizOne.delete({
      where: { id: parseInt(id) },
    });
    res.status(200).json({ message: "QuizOne deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const addQuizTwo = async (req, res) => {
  const { question, answer } = req.body;
  const { id } = req.params;

  const lesson = await prisma.lesson.findUnique({
    where: { id: parseInt(id) },
    select: { lessonNumber: true },
  });

  try {
    const newQuizTwo = await prisma.quizTwo.create({
      data: {
        question,
        answer,
        lessonId: lesson.lessonNumber,
      },
    });
    res
      .status(201)
      .json({ message: "QuizTwo added successfully", data: newQuizTwo });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const editQuizTwo = async (req, res) => {
  const { id } = req.params;
  const { question, answer, lessonId } = req.body;

  try {
    const updatedQuizTwo = await prisma.quizTwo.update({
      where: { id: parseInt(id) },
      data: {
        question,
        answer,
      },
    });
    res
      .status(200)
      .json({ message: "QuizTwo updated successfully", data: updatedQuizTwo });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteQuizTwo = async (req, res) => {
  const { id } = req.params;

  try {
    await prisma.quizTwo.delete({
      where: { id: parseInt(id) },
    });
    res.status(200).json({ message: "QuizTwo deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const addQuizThree = async (req, res) => {
  const { question, option1, option2, option3, option4, answer } = req.body;
  const { id } = req.params;

  const lesson = await prisma.lesson.findUnique({
    where: { id: parseInt(id) },
    select: { lessonNumber: true },
  });

  try {
    const newQuizThree = await prisma.quizThree.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId: lesson.lessonNumber,
      },
    });
    res
      .status(201)
      .json({ message: "QuizThree added successfully", data: newQuizThree });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const editQuizThree = async (req, res) => {
  const { id } = req.params;
  const { question, option1, option2, option3, option4, answer, lessonId } =
    req.body;

  try {
    const updatedQuizThree = await prisma.quizThree.update({
      where: { id: parseInt(id) },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
      },
    });
    res
      .status(200)
      .json({
        message: "QuizThree updated successfully",
        data: updatedQuizThree,
      });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteQuizThree = async (req, res) => {
  const { id } = req.params;

  try {
    await prisma.quizThree.delete({
      where: { id: parseInt(id) },
    });
    res.status(200).json({ message: "QuizThree deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

// exam quiz

const addExamQuiz = async (req, res) => {
  const { question, option1, option2, option3, option4, answer, lessonId } =
    req.body;

  try {
    const newExamQuiz = await prisma.examQuizes.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId,
      },
    });
    res
      .status(201)
      .json({ message: "Exam Quiz added successfully", data: newExamQuiz });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const editExamQuiz = async (req, res) => {
  const { id } = req.params;
  const { question, option1, option2, option3, option4, answer, lessonId } =
    req.body;

  try {
    const updatedExamQuiz = await prisma.examQuizes.update({
      where: { id: parseInt(id) },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId,
      },
    });
    res
      .status(200)
      .json({
        message: "Exam Quiz updated successfully",
        data: updatedExamQuiz,
      });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteExamQuiz = async (req, res) => {
  const { id } = req.params;

  try {
    await prisma.examQuizes.delete({
      where: { id: parseInt(id) },
    });
    res.status(200).json({ message: "Exam Quiz deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

//gre questions

const addGreQuiz = async (req, res) => {
  const {
    question,
    option1,
    option2,
    option3,
    option4,
    option5,
    answer,
    lessonId,
  } = req.body;

  try {
    const newGreQuiz = await prisma.greQuizes.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        option5,
        answer,
        lessonId, // No need to parse as integer anymore
      },
    });
    res
      .status(201)
      .json({ message: "GRE Quiz added successfully", data: newGreQuiz });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const editGreQuiz = async (req, res) => {
  const { id } = req.params;
  const {
    question,
    option1,
    option2,
    option3,
    option4,
    option5,
    answer,
    lessonId,
  } = req.body;

  try {
    const updatedGreQuiz = await prisma.greQuizes.update({
      where: { id: id },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        option5,
        answer,
        lessonId,
      },
    });
    res
      .status(200)
      .json({ message: "GRE Quiz updated successfully", data: updatedGreQuiz });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteGreQuiz = async (req, res) => {
  const { id } = req.params;

  try {
    await prisma.greQuizes.delete({
      where: { id: id },
    });
    res.status(200).json({ message: "GRE Quiz deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

//

const getAllExamQuizByLessonId = async (req, res) => {
  const { lessonId } = req.params;

  try {
    const quizOnes = await prisma.examQuizes.findMany({
      where: { lessonNumber: parseInt(lessonId) },
    });
    res
      .status(200)
      .json({ message: "QuizOnes retrieved successfully", data: quizOnes });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getAllGreQuizesByLessonId = async (req, res) => {
  const { lessonId } = req.params;

  try {
    const greQuizzes = await prisma.greQuizes.findMany({
      where: { lessonId: lessonId },
    });
    res
      .status(200)
      .json({
        message: "GRE Quizzes retrieved successfully",
        data: greQuizzes,
      });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const addGreLesson = async (req, res) => {
  const { title, content, lessonNumber } = req.body;

  if (!title || !content) {
    return res
      .status(400)
      .json({ message: "Please provide title and content" });
  }

  try {
    const lesson = await prisma.greLesson.create({
      data: {
        title,
        content,
        lessonNumber,
      },
    });

    res.status(200).json({ message: `New GRE lesson created`, data: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const getAllGreLessons = async (req, res) => {
  try {
    const lessons = await prisma.greLesson.findMany({
      include: {
        GreQuizes: true,
      },
    });
    res
      .status(200)
      .json({ message: "GRE Lessons retrieved successfully", data: lessons });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getGreLessonById = async (req, res) => {
  const { id } = req.params;

  try {
    const lesson = await prisma.greLesson.findUnique({
      where: { id },
      include: {
        GreQuizes: true,
      },
    });

    if (!lesson) {
      return res.status(404).json({ message: "GRE Lesson not found" });
    }

    res
      .status(200)
      .json({ message: "GRE Lesson retrieved successfully", data: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const updateGreLesson = async (req, res) => {
  const { id } = req.params;
  const { title, content } = req.body;

  try {
    const updatedLesson = await prisma.greLesson.update({
      where: { id },
      data: {
        title,
        content,
      },
    });

    res
      .status(200)
      .json({
        message: "GRE Lesson updated successfully",
        data: updatedLesson,
      });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteGreLesson = async (req, res) => {
  const { id } = req.params;

  try {
    // First delete all related quizzes
    await prisma.greQuizes.deleteMany({
      where: { lessonId: id },
    });

    // Then delete the lesson
    await prisma.greLesson.delete({
      where: { id },
    });

    res
      .status(200)
      .json({ message: "GRE Lesson and related quizzes deleted successfully" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const addExamLesson = async (req, res) => {
  const { title, content, lessonNumber } = req.body;

  if (!title || !content || !lessonNumber) {
    return res
      .status(400)
      .json({ message: "Please provide title and content" });
  }

  try {
    const lesson = await prisma.examLesson.create({
      data: {
        title,
        content,
        lessonNumber,
      },
    });

    res.status(200).json({ message: `New Exam lesson created`, data: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err });
  }
};

const getAllQuizOneByLessonId = async (req, res) => {
  const { lessonId } = req.params;

  try {
    const quizOnes = await prisma.quizOne.findMany({
      where: { lessonId: parseInt(lessonId) },
    });
    res
      .status(200)
      .json({ message: "QuizOnes retrieved successfully", data: quizOnes });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getAllQuizTwoByLessonId = async (req, res) => {
  const { lessonId } = req.params;

  try {
    const quizTwos = await prisma.quizTwo.findMany({
      where: { lessonId: parseInt(lessonId) },
    });
    res
      .status(200)
      .json({ message: "QuizTwos retrieved successfully", data: quizTwos });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getAllQuizThreeByLessonId = async (req, res) => {
  const { lessonId } = req.params;

  try {
    const quizThrees = await prisma.quizThree.findMany({
      where: { lessonId: parseInt(lessonId) },
    });
    res
      .status(200)
      .json({ message: "QuizThrees retrieved successfully", data: quizThrees });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const addQuizDiscussion = async (req, res) => {
  const { comment, lesson, parentId } = req.body;
  const userId = parseInt(req.user);

  if (!comment || !lesson) {
    return res.status(400).json({ error: "Pass valid data" });
  }

  try {
    const lessonId = parseInt(lesson);
    const discussion = await prisma.lessonDiscussion.create({
      data: {
        userId,
        comment,
        lessonId,
        parentId: parentId ? parseInt(parentId) : null,
      },
    });

    res.status(201).json({ message: "Comment added", discussion });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const voteDiscussion = async (req, res) => {
  const { discussionId, voteType } = req.body;
  const userId = req.user;

  if (!discussionId || !["UPVOTE", "DOWNVOTE"].includes(voteType)) {
    return res.status(400).json({ error: "Invalid vote data" });
  }

  try {
    const discussion = await prisma.lessonDiscussion.findUnique({
      where: { id: parseInt(discussionId) },
    });

    if (!discussion) {
      return res.status(404).json({ error: "Discussion not found" });
    }

    const existingVote = await prisma.vote.findUnique({
      where: {
        userId_discussionId: { userId, discussionId: parseInt(discussionId) },
      },
    });

    if (existingVote) {
      // If user is changing vote type, update it
      if (existingVote.voteType !== voteType) {
        await prisma.vote.update({
          where: { id: existingVote.id },
          data: { voteType },
        });
        return res.status(200).json({ message: "Vote updated" });
      }

      // If user clicks the same vote again, remove the vote (toggle behavior)
      await prisma.vote.delete({ where: { id: existingVote.id } });
      return res.status(200).json({ message: "Vote removed" });
    }

    // Create a new vote
    await prisma.vote.create({
      data: {
        userId,
        discussionId: parseInt(discussionId),
        voteType,
      },
    });

    res.status(200).json({ message: "Vote recorded" });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getQuizDiscussion = async (req, res) => {
  const { lessonId, perPage, pageNumber } = req.query;

  try {
    const limit = parseInt(perPage) || 10;
    const page = parseInt(pageNumber) || 1;

    if (page < 1 || limit < 1) {
      return res.status(400).json({ message: "Invalid pagination parameters" });
    }

    const totalItems = await prisma.lessonDiscussion.count({
      where: { lessonId: parseInt(lessonId), parentId: null }, // Only main discussions
    });

    const totalPages = Math.ceil(totalItems / limit);

    if (page > totalPages && totalPages > 0) {
      return res.status(400).json({ message: "Page number out of range" });
    }

    const discussions = await prisma.lessonDiscussion.findMany({
      where: { lessonId: parseInt(lessonId), parentId: null }, // Only main discussions
      include: { user: true, votes: true },
      orderBy: { createdAt: "desc" },
      skip: (page - 1) * limit,
      take: limit,
    });

    // Fetch replies separately for each main discussion
    for (let discussion of discussions) {
      discussion.replies = await prisma.lessonDiscussion.findMany({
        where: { parentId: discussion.id }, // Get replies for the current discussion
        include: { user: true },
      });
    }

    res.status(200).json({
      data: discussions,
      totalItems,
      totalPages,
      currentPage: page,
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

// Add missing functions as placeholders
const getAllExamLessons = async (req, res) => {
  try {
    const lessons = await prisma.examLesson.findMany({
      include: {
        ExamQuizes: true,
      },
    });
    res
      .status(200)
      .json({ message: "Exam Lessons retrieved successfully", data: lessons });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const getExamLessonById = async (req, res) => {
  const { id } = req.params;

  try {
    const lesson = await prisma.examLesson.findUnique({
      where: { id },
      include: {
        ExamQuizes: true,
      },
    });

    if (!lesson) {
      return res.status(404).json({ message: "Exam Lesson not found" });
    }

    res
      .status(200)
      .json({ message: "Exam Lesson retrieved successfully", data: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const updateExamLesson = async (req, res) => {
  const { id } = req.params;
  const { title, content, lessonNumber } = req.body;

  try {
    const lesson = await prisma.examLesson.update({
      where: { id },
      data: {
        title,
        content,
        lessonNumber,
      },
    });

    res
      .status(200)
      .json({ message: "Exam Lesson updated successfully", data: lesson });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

const deleteExamLesson = async (req, res) => {
  const { id } = req.params;

  try {
    // First delete all related quizzes
    await prisma.examQuizes.deleteMany({
      where: { lessonId: id },
    });

    // Then delete the lesson
    await prisma.examLesson.delete({
      where: { id },
    });

    res
      .status(200)
      .json({
        message: "Exam Lesson and related quizzes deleted successfully",
      });
  } catch (err) {
    console.error(err);
    res.status(500).json({ message: err.message });
  }
};

module.exports = {
  addSingleLesson,
  updateLesson,
  getAllLesson,
  getLessonDetailsData,
  getExamQuizListByLesson,
  getAllPromotion,
  addPromotion,
  getAllLessonByPage,
  addQuizOne,
  editQuizOne,
  deleteQuizOne,
  addQuizTwo,
  editQuizTwo,
  deleteQuizTwo,
  addQuizThree,
  editQuizThree,
  deleteQuizThree,
  getAllQuizOneByLessonId,
  getAllQuizTwoByLessonId,
  getAllQuizThreeByLessonId,
  getGreQuizListByLesson,
  addQuizDiscussion,
  getQuizDiscussion,
  addGreQuiz,
  editGreQuiz,
  deleteGreQuiz,
  addExamQuiz,
  editExamQuiz,
  deleteExamQuiz,
  getAllExamQuizByLessonId,
  getAllGreQuizesByLessonId,
  voteDiscussion,
  addGreLesson,
  getAllGreLessons,
  getGreLessonById,
  updateGreLesson,
  deleteGreLesson,
  addExamLesson,
  getAllExamLessons,
  getExamLessonById,
  updateExamLesson,
  deleteExamLesson,
};
