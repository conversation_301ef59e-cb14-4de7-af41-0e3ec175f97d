{"name": "news_lexica_backend", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "start": "node app.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.9.0", "axios": "^1.6.8", "bcrypt": "^5.1.1", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "express": "^4.18.2", "express-fileupload": "^1.4.3", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "firebase-admin": "^12.0.0", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql": "^2.18.1", "uuid": "^9.0.1"}, "devDependencies": {"@types/jest": "^30.0.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "prisma": "^6.9.0", "supertest": "^7.1.1"}}