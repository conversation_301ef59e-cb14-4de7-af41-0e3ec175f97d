const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");

const { addSingleLesson,updateLesson,getAllLesson, getLessonDetailsData,getExamQuizListByLesson,getAllPromotion,addPromotion,getAllLessonByPage,
    addQuizOne,editQuizOne,deleteQuizOne,addQuizTwo,editQuizTwo,deleteQuizTwo,addQuizThree,editQuizThree,deleteQuizThree,
    getAllQuizOneByLessonId,getAllQuizTwoByLessonId,getAllQuizThreeByLessonId,getGreQuizListByLesson,addQuizDiscussion,getQuizDiscussion,
    addGreQuiz,editGreQuiz,deleteGreQuiz,addExamQuiz,editExamQuiz,deleteExamQuiz,getAllExamQuizByLessonId,getAllGreQuizesByLessonId,voteDiscussion,
    addGreLesson, getAllGreLessons, getGreLessonById, updateGreLesson, deleteGreLesson,
    addExamLesson, getAllExamLessons, getExamLessonById, updateExamLesson, deleteExamLesson } = require("../../controller/v2/lesson_controller");
router.post("/addLesson", addSingleLesson);
router.get("/", getAllLesson);

router.post("/addPromotion", addPromotion);
router.post("/getAllPromotion", getAllPromotion);

router.get("/getExamQuizes/", getExamQuizListByLesson);
router.get("/getGreQuizListByLesson", getGreQuizListByLesson);
router.get("/getAllLessonByPage", getAllLessonByPage);


router.post('/quizOne/:id', addQuizOne);
router.put('/quizOne/:id', editQuizOne);
router.delete('/quizOne/:id', deleteQuizOne);
router.get('/quizOne/lessons/:lessonId', getAllQuizOneByLessonId);



router.post('/quizTwo/:id', addQuizTwo);
router.put('/quizTwo/:id', editQuizTwo);
router.delete('/quizTwo/:id', deleteQuizTwo);
router.get('/quizTwo/lesson/:lessonId', getAllQuizTwoByLessonId);



router.post('/quizThree/:id', addQuizThree);
router.put('/quizThree/:id', editQuizThree);
router.delete('/quizThree/:id', deleteQuizThree);
router.get('/quizThree/lesson/:lessonId', getAllQuizThreeByLessonId);

router.post("/addQuizDiscussion",verifyJWT, addQuizDiscussion);
router.get("/getQuizDiscussion", getQuizDiscussion);




router.post('/greQuiz', addGreQuiz);
router.put('/greQuiz/:id', editGreQuiz);
router.delete('/greQuiz/:id', deleteGreQuiz);
router.get('/greQuiz/lesson/:lessonId', getAllGreQuizesByLessonId);


router.post('/examQuiz', addExamQuiz);
router.put('/examQuiz/:id', editExamQuiz);
router.delete('/examQuiz/:id', deleteExamQuiz);
router.get('/examQuiz/lesson/:lessonId', getAllExamQuizByLessonId);



router.post('/vote', verifyJWT,voteDiscussion);

router.get("/:id", getLessonDetailsData).put("/:id", updateLesson);

// Add new routes for GRE lessons
router.post("/greLesson", addGreLesson);
router.get("/greLesson", getAllGreLessons);
router.get("/greLesson/:id", getGreLessonById);
router.put("/greLesson/:id", updateGreLesson);
router.delete("/greLesson/:id", deleteGreLesson);

// Add new routes for Exam lessons
router.post("/examLesson", addExamLesson);
router.get("/examLesson", getAllExamLessons);
router.get("/examLesson/:id", getExamLessonById);
router.put("/examLesson/:id", updateExamLesson);
router.delete("/examLesson/:id", deleteExamLesson);

// Update the module exports to include the new controller functions
module.exports = {
    addSingleLesson, updateLesson, getAllLesson, getLessonDetailsData, getExamQuizListByLesson, getAllPromotion, addPromotion, getAllLessonByPage,
    addQuizOne, editQuizOne, deleteQuizOne, addQuizTwo, editQuizTwo, deleteQuizTwo, addQuizThree, editQuizThree, deleteQuizThree,
    getAllQuizOneByLessonId, getAllQuizTwoByLessonId, getAllQuizThreeByLessonId, getGreQuizListByLesson, addQuizDiscussion, getQuizDiscussion,
    addGreQuiz, editGreQuiz, deleteGreQuiz, addExamQuiz, editExamQuiz, deleteExamQuiz, getAllExamQuizByLessonId, getAllGreQuizesByLessonId, voteDiscussion,
    // New functions
    addGreLesson, getAllGreLessons, getGreLessonById, updateGreLesson, deleteGreLesson,
    addExamLesson, getAllExamLessons, getExamLessonById, updateExamLesson, deleteExamLesson
}
