const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");
const { verifyRoles } = require("../../middleware/verifyRoles");
const { validateBody, validateQuery, validateParams } = require("../../middleware/validateRequest");
const { fileExtLimiter } = require("../../middleware/fileExtLimiter");
const { fileSizeLimiter } = require("../../middleware/fileSizeLimiter");
const Joi = require("joi");

const {
  createPromotion,
  getPromotions,
  getPromotionById,
  updatePromotion,
  deletePromotion,
} = require("../../controller/v2/promotions.controller");

// Validation schemas
const createPromotionSchema = Joi.object({
  name: Joi.string().required().min(1).max(200),
});

const updatePromotionSchema = Joi.object({
  name: Joi.string().min(1).max(200),
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().positive().default(1),
  limit: Joi.number().integer().positive().max(100).default(10),
});

const idSchema = Joi.object({
  id: Joi.string().required(),
});

// File upload middleware for images
const imageUploadMiddleware = [
  fileExtLimiter(['.jpg', '.jpeg', '.png', '.gif', '.webp']),
  fileSizeLimiter,
];

/**
 * @route   GET /api/v2/promotions
 * @desc    Get all promotions with pagination
 * @access  Public
 * @query   ?page=1&limit=10
 */
router.get("/", validateQuery(paginationSchema), getPromotions);

/**
 * @route   POST /api/v2/promotions
 * @desc    Create a new promotion
 * @access  Private (Admin only)
 * @body    { name: string }
 * @files   photo (required)
 */
router.post(
  "/",
  verifyJWT,
  verifyRoles("ADMIN"),
  ...imageUploadMiddleware,
  validateBody(createPromotionSchema),
  createPromotion
);

/**
 * @route   GET /api/v2/promotions/:id
 * @desc    Get a specific promotion by ID
 * @access  Public
 */
router.get("/:id", validateParams(idSchema), getPromotionById);

/**
 * @route   PUT /api/v2/promotions/:id
 * @desc    Update a specific promotion
 * @access  Private (Admin only)
 * @body    { name?: string }
 * @files   photo (optional)
 */
router.put(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(idSchema),
  ...imageUploadMiddleware,
  validateBody(updatePromotionSchema),
  updatePromotion
);

/**
 * @route   DELETE /api/v2/promotions/:id
 * @desc    Delete a specific promotion
 * @access  Private (Admin only)
 */
router.delete(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(idSchema),
  deletePromotion
);

module.exports = router;
