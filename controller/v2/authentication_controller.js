const prisma = require("../../prisma/index");
const { sendSms } = require("../../utils/sendOtp");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const config = require("../../config/environment");

const testServer = async (req, res) => {
  return res.status(200).json({ message: "okay" });
};

const handleLogin = async (req, res) => {
  try {
    const { phone, password, token } = req.body;

    // Input validation is now handled by middleware, but double-check
    if (!phone || !password) {
      return res.status(400).json({
        success: false,
        message: "Phone number and password are required",
      });
    }

    const user = await prisma.user.findUnique({
      where: {
        phone: phone,
      },
      include: {
        role: true,
      },
    });

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    const match = await bcrypt.compare(password, user.password);

    if (!match) {
      return res.status(401).json({
        success: false,
        message: "Invalid credentials",
      });
    }

    // Generate JWT tokens with environment-based configuration
    const roles = user.role.map((role) => role.role);

    const accessToken = jwt.sign(
      { id: user.id, roles: roles },
      config.jwt.accessTokenSecret,
      { expiresIn: config.jwt.accessTokenExpiry }
    );

    const refreshToken = jwt.sign(
      { id: user.id },
      config.jwt.refreshTokenSecret,
      { expiresIn: config.jwt.refreshTokenExpiry }
    );

    // Update user with new refresh token
    await prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        refreshToken: refreshToken,
        token: token,
      },
    });

    // Set secure cookie
    res.cookie("jwt", accessToken, {
      httpOnly: true,
      sameSite: config.nodeEnv === "production" ? "None" : "Lax",
      secure: config.nodeEnv === "production",
      maxAge: config.security.cookieMaxAge,
    });

    return res.json({
      success: true,
      accessToken,
      refreshToken,
      user: {
        id: user.id,
        name: user.name,
        phone: user.phone,
        roles: roles,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    return res.status(500).json({
      success: false,
      message: "Internal server error",
    });
  }
};

const handleLoginAdmin = async (req, res) => {
  const { phone, password, token } = req.body;

  if (!phone || !password) {
    return res
      .status(400)
      .json({ message: "Phone number and Password required!!" });
  }

  const user = await prisma.user.findUnique({
    where: {
      phone: phone,
    },
    include: {
      role: true,
    },
  });

  if (!user) {
    return res.status(400).json({ message: "No user exist" });
  }

  if (user.role[0].role !== "ADMIN") {
    return res.status(400).json({ message: "You are not an admin" });
  }

  const match = await bcrypt.compare(password, user.password);

  if (match) {
    // JWT

    const roles = Object.values(user.role).map((role) => {
      return role.role;
    });

    const accessToken = jwt.sign(
      { id: user.id, roles: roles },
      process.env.ACCESS_TOKEN_SECRET,
      { expiresIn: "1 min" }
    );

    const refreshToken = jwt.sign(
      { id: user.id },
      process.env.REFRESH_TOKEN_SECRET,
      { expiresIn: "1200d" }
    );

    await prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        refreshToken: refreshToken,
        token: token,
      },
    });

    res.cookie("jwt", accessToken, {
      httpOnly: true,
      sameSite: "None",
      secure: true,
      maxAge: 24 * 60 * 60 * 1000,
    });

    return res.json({ accessToken, refreshToken });
  } else {
    return res.status(401).json({ message: "Wrong Password!" });
  }
};

const sendOtp = async (req, res) => {
  try {
    const { phone } = req.body; // Changed from query to body for security

    if (!phone) {
      return res.status(400).json({
        success: false,
        message: "Phone number is required",
      });
    }

    // Handle test phone number in development only
    if (config.nodeEnv === "development" && phone === config.test.phoneNumber) {
      const testOtpHash = await bcrypt.hash(
        config.test.otp,
        config.security.bcryptRounds
      );
      return res.status(200).json({
        success: true,
        message: testOtpHash,
        isTest: true,
      });
    }

    // Generate secure OTP
    const num = Math.floor(1000 + Math.random() * 9000);
    const otpString = num.toString();

    // Hash the OTP with higher security rounds
    const hashedOtp = await bcrypt.hash(
      otpString,
      config.security.bcryptRounds
    );

    // Prepare SMS data using environment variables
    const phoneNumbers = `88${phone}`;
    const message = `Welcome to News Lexica, your OTP is ${num}`;

    // Send SMS using environment configuration
    const smsResponse = await sendSms(
      config.sms.apiKey,
      config.sms.senderId,
      phoneNumbers,
      message
    );

    console.log("SMS sent successfully:", smsResponse);

    return res.status(200).json({
      success: true,
      message: hashedOtp,
      expiresIn: "15 minutes", // OTP validity period
    });
  } catch (error) {
    console.error("Error sending OTP:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to send OTP. Please try again.",
    });
  }
};

const verifyOtp = async (req, res) => {
  try {
    const { hashedOtp, enteredOtp, phone } = req.body; // Changed from query to body

    if (!hashedOtp || !enteredOtp || !phone) {
      return res.status(400).json({
        success: false,
        message: "Hashed OTP, entered OTP, and phone number are required",
      });
    }

    // Verify OTP
    const isValidOtp = await bcrypt.compare(enteredOtp, hashedOtp);

    if (isValidOtp) {
      return res.status(200).json({
        success: true,
        message: "OTP verified successfully",
        verified: true,
      });
    } else {
      return res.status(400).json({
        success: false,
        message: "Invalid OTP",
        verified: false,
      });
    }
  } catch (error) {
    console.error("Error verifying OTP:", error);
    return res.status(500).json({
      success: false,
      message: "Failed to verify OTP. Please try again.",
    });
  }
};

const newAccessToken = async (req, res) => {
  const { refreshToken } = req.body;
  if (!refreshToken) {
    return res.status(400).json({ message: "Refresh token is required!" });
  }
  try {
    const user = await prisma.user.findUnique({
      where: {
        refreshToken: refreshToken,
      },
      include: {
        role: true,
      },
    });
    if (!user) {
      return res.status(404).json({ message: "User not found!" });
    }

    const roles = Object.values(user.role).map((role) => {
      return role.role;
    });
    const accessToken = jwt.sign(
      { id: user.id, roles: roles },
      process.env.ACCESS_TOKEN_SECRET,
      { expiresIn: "1 min" }
    );
    const newRefreshToken = jwt.sign(
      { id: user.id },
      process.env.REFRESH_TOKEN_SECRET,
      { expiresIn: "30d" }
    );

    await prisma.user.update({
      where: {
        id: user.id,
      },
      data: {
        refreshToken: newRefreshToken,
      },
    });
    return res.json({ accessToken, newRefreshToken });
  } catch (error) {
    console.log(error);
    return res.status(500).json({ message: "Internal server error!" });
  }
};

module.exports = {
  handleLogin,
  sendOtp,
  verifyOtp,
  testServer,
  newAccessToken,
  handleLoginAdmin,
};
