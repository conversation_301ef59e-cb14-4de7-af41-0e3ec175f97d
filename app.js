const express = require("express");
const app = express();
const path = require("path");
const cors = require("cors");
const cookieParser = require("cookie-parser");
const fileUpload = require("express-fileupload");
const helmet = require("helmet");

// Import configuration
const config = require("./config/environment");

// Import logging and monitoring
const logger = require("./utils/logger");
const {
  requestLogger,
  enhancedRequestLogger,
  errorRequestLogger,
  rateLimitLogger,
} = require("./middleware/requestLogger");
const {
  requestPerformanceMonitor,
  startPeriodicMonitoring,
} = require("./middleware/performanceMonitor");

// Import middleware
const { credentials } = require("./middleware/credentials");
const errorHandler = require("./middleware/errorHandler");
const { generalLimiter } = require("./middleware/rateLimiter");

const corsOptions = require("./config/corsOptions");

const PORT = config.port;

//v1 routes
const authenticationRoutes = require("./routes/authentication_routes");
const registerRouter = require("./routes/register_routes");
const lessonRouter = require("./routes/lesson_routes");
const userRouter = require("./routes/user_routes");
const ieltsRouter = require("./routes/ielts_module_routes");

//v2 routes
const authenticationRoutes2 = require("./routes/v2/authentication_routes");
const registerRouter2 = require("./routes/v2/register_routes");
const lessonRouter2 = require("./routes/v2/lesson_routes");
const userRouter2 = require("./routes/v2/user_routes");
const ieltsRouter2 = require("./routes/v2/ielts_module_routes");

// Security middlewares (should be first)
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"],
      },
    },
    crossOriginEmbedderPolicy: false,
  })
);

// Logging and monitoring middleware
app.use(requestLogger);
app.use(enhancedRequestLogger);
app.use(requestPerformanceMonitor);
app.use(rateLimitLogger);

// Rate limiting (apply to all requests)
app.use(generalLimiter);

// Built-in middlewares
app.use(express.urlencoded({ extended: false, limit: "10mb" }));
app.use(express.json({ limit: "10mb" }));
app.use(express.static(path.join(__dirname, "public")));
app.use(express.static("public"));
app.use(
  fileUpload({
    createParentPath: true,
    limits: { fileSize: config.fileUpload.maxSizeMB * 1024 * 1024 },
    abortOnLimit: true,
  })
);
app.use(cookieParser());
app.use(
  "/public/files",
  express.static(path.join(__dirname, "public", "files"))
);

// Custom middlewares
app.use(credentials);

// Third-party middlewares
app.use(cors(corsOptions));

// Route handlers
app.use("/auth", authenticationRoutes);
app.use("/register", registerRouter);
app.use("/lesson", lessonRouter);
app.use("/user", userRouter);
app.use("/ielts", ieltsRouter);

// V2 API Routes - RESTful and properly organized
app.use("/api/v2/auth", authenticationRoutes2);
app.use("/api/v2/users", registerRouter2);
app.use("/api/v2/lessons", require("./routes/v2/lessons.routes"));
app.use("/api/v2/quizzes", require("./routes/v2/quizzes.routes"));
app.use("/api/v2/gre-lessons", require("./routes/v2/gre-lessons.routes"));
app.use("/api/v2/exam-lessons", require("./routes/v2/exam-lessons.routes"));
app.use("/api/v2/promotions", require("./routes/v2/promotions.routes"));
app.use("/api/v2/discussions", require("./routes/v2/discussions.routes"));

// Legacy V2 routes (deprecated - use new RESTful routes above)
app.use("/api/v2/lesson", lessonRouter2);
app.use("/api/v2/user", userRouter2);
app.use("/api/v2/ielts", ieltsRouter2);

app.get("/", (req, res) => {
  res.send("Hello from the Server-side!");
});

app.use("*", (req, res) => {
  res.status(404).json({
    success: false,
    message: "Invalid Api route",
  });
});
// Error handling middleware (should be last)
app.use(errorRequestLogger);
app.use(errorHandler);

// Start periodic monitoring
startPeriodicMonitoring();

// Start the server
app.listen(PORT, () => {
  logger.info(`Server started on port ${PORT}`, {
    port: PORT,
    environment: config.nodeEnv,
    timestamp: new Date().toISOString(),
  });
  console.log(`Server initiated at http://localhost:${PORT}`);
});
