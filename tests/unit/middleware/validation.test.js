const { validateBody, validateQuery, validateParams } = require('../../../middleware/validateRequest');
const Joi = require('joi');

describe('Validation Middleware', () => {
  let req, res, next;

  beforeEach(() => {
    req = {
      body: {},
      query: {},
      params: {},
    };
    res = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn(),
    };
    next = jest.fn();
  });

  describe('validateBody', () => {
    const schema = Joi.object({
      name: Joi.string().required(),
      email: Joi.string().email().required(),
      age: Joi.number().integer().min(18),
    });

    it('should pass validation with valid data', () => {
      req.body = {
        name: '<PERSON>',
        email: '<EMAIL>',
        age: 25,
      };

      const middleware = validateBody(schema);
      middleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(res.status).not.toHaveBeenCalled();
    });

    it('should fail validation with invalid data', () => {
      req.body = {
        name: '', // Invalid: empty string
        email: 'invalid-email', // Invalid: not an email
        age: 15, // Invalid: below minimum
      };

      const middleware = validateBody(schema);
      middleware(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Validation failed',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'name',
            message: expect.stringContaining('not allowed to be empty'),
          }),
          expect.objectContaining({
            field: 'email',
            message: expect.stringContaining('valid email'),
          }),
          expect.objectContaining({
            field: 'age',
            message: expect.stringContaining('greater than or equal to 18'),
          }),
        ]),
      });
    });

    it('should strip unknown fields', () => {
      req.body = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25,
        unknownField: 'should be removed',
      };

      const middleware = validateBody(schema);
      middleware(req, res, next);

      expect(req.body).not.toHaveProperty('unknownField');
      expect(req.body).toEqual({
        name: 'John Doe',
        email: '<EMAIL>',
        age: 25,
      });
      expect(next).toHaveBeenCalled();
    });

    it('should convert types when possible', () => {
      req.body = {
        name: 'John Doe',
        email: '<EMAIL>',
        age: '25', // String that should be converted to number
      };

      const middleware = validateBody(schema);
      middleware(req, res, next);

      expect(req.body.age).toBe(25); // Should be converted to number
      expect(typeof req.body.age).toBe('number');
      expect(next).toHaveBeenCalled();
    });
  });

  describe('validateQuery', () => {
    const schema = Joi.object({
      page: Joi.number().integer().positive().default(1),
      limit: Joi.number().integer().positive().max(100).default(10),
      search: Joi.string().optional(),
    });

    it('should pass validation with valid query parameters', () => {
      req.query = {
        page: '2',
        limit: '20',
        search: 'test',
      };

      const middleware = validateQuery(schema);
      middleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(req.query).toEqual({
        page: 2,
        limit: 20,
        search: 'test',
      });
    });

    it('should apply default values', () => {
      req.query = {}; // Empty query

      const middleware = validateQuery(schema);
      middleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(req.query).toEqual({
        page: 1,
        limit: 10,
      });
    });

    it('should fail validation with invalid query parameters', () => {
      req.query = {
        page: 'invalid',
        limit: '200', // Exceeds maximum
      };

      const middleware = validateQuery(schema);
      middleware(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Query validation failed',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'page',
            message: expect.stringContaining('number'),
          }),
          expect.objectContaining({
            field: 'limit',
            message: expect.stringContaining('less than or equal to 100'),
          }),
        ]),
      });
    });
  });

  describe('validateParams', () => {
    const schema = Joi.object({
      id: Joi.number().integer().positive().required(),
      slug: Joi.string().optional(),
    });

    it('should pass validation with valid parameters', () => {
      req.params = {
        id: '123',
        slug: 'test-slug',
      };

      const middleware = validateParams(schema);
      middleware(req, res, next);

      expect(next).toHaveBeenCalled();
      expect(req.params).toEqual({
        id: 123,
        slug: 'test-slug',
      });
    });

    it('should fail validation with invalid parameters', () => {
      req.params = {
        id: 'invalid',
      };

      const middleware = validateParams(schema);
      middleware(req, res, next);

      expect(next).not.toHaveBeenCalled();
      expect(res.status).toHaveBeenCalledWith(400);
      expect(res.json).toHaveBeenCalledWith({
        success: false,
        message: 'Parameter validation failed',
        errors: expect.arrayContaining([
          expect.objectContaining({
            field: 'id',
            message: expect.stringContaining('number'),
          }),
        ]),
      });
    });
  });

  describe('Error handling', () => {
    it('should handle Joi validation errors gracefully', () => {
      const invalidSchema = null; // This will cause an error

      expect(() => {
        const middleware = validateBody(invalidSchema);
        middleware(req, res, next);
      }).toThrow();
    });
  });
});
