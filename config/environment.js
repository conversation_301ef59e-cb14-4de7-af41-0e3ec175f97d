require('dotenv').config();

const config = {
  // Server Configuration
  port: process.env.PORT || 5000,
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Database Configuration
  databaseUrl: process.env.DATABASE_URL,
  
  // JWT Configuration
  jwt: {
    accessTokenSecret: process.env.ACCESS_TOKEN_SECRET,
    refreshTokenSecret: process.env.REFRESH_TOKEN_SECRET,
    accessTokenExpiry: process.env.ACCESS_TOKEN_EXPIRY || '15m',
    refreshTokenExpiry: process.env.REFRESH_TOKEN_EXPIRY || '7d'
  },
  
  // SMS Configuration
  sms: {
    apiKey: process.env.SMS_API_KEY,
    senderId: process.env.SMS_SENDER_ID,
    apiUrl: process.env.SMS_API_URL || 'http://bulksmsbd.net/api/smsapi'
  },
  
  // Test Configuration (development only)
  test: {
    phoneNumber: process.env.TEST_PHONE_NUMBER,
    otp: process.env.TEST_OTP
  },
  
  // Rate Limiting Configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 900000, // 15 minutes
    maxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 5
  },
  
  // File Upload Configuration
  fileUpload: {
    maxSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB) || 5,
    allowedAudioTypes: process.env.ALLOWED_AUDIO_TYPES?.split(',') || [
      'audio/mpeg',
      'audio/mp3', 
      'audio/wav',
      'audio/ogg',
      'audio/mp4'
    ]
  },
  
  // Security Configuration
  security: {
    bcryptRounds: 12,
    cookieMaxAge: 24 * 60 * 60 * 1000 // 24 hours
  }
};

// Validate required environment variables
const requiredEnvVars = [
  'DATABASE_URL',
  'ACCESS_TOKEN_SECRET',
  'REFRESH_TOKEN_SECRET'
];

const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);

if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars);
  process.exit(1);
}

// Warn about development-only configurations in production
if (config.nodeEnv === 'production') {
  if (config.test.phoneNumber || config.test.otp) {
    console.warn('WARNING: Test configurations detected in production environment');
  }
  
  if (config.jwt.accessTokenSecret === 'your_super_secure_access_token_secret_here_change_this_in_production') {
    console.error('ERROR: Default JWT secrets detected in production. Please change them!');
    process.exit(1);
  }
}

module.exports = config;
