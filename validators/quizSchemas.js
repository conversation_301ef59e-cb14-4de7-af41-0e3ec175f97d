const Joi = require('joi');

// Quiz validation schemas
const quizSchemas = {
  // Quiz Type One (Multiple Choice) validation
  createQuizOne: Joi.object({
    question: Joi.string()
      .min(5)
      .max(500)
      .trim()
      .required()
      .messages({
        'string.min': 'Question must be at least 5 characters long',
        'string.max': 'Question must not exceed 500 characters',
        'any.required': 'Question is required',
      }),
    option1: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 1 must be at least 1 character long',
        'string.max': 'Option 1 must not exceed 200 characters',
        'any.required': 'Option 1 is required',
      }),
    option2: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 2 must be at least 1 character long',
        'string.max': 'Option 2 must not exceed 200 characters',
        'any.required': 'Option 2 is required',
      }),
    option3: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 3 must be at least 1 character long',
        'string.max': 'Option 3 must not exceed 200 characters',
        'any.required': 'Option 3 is required',
      }),
    option4: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 4 must be at least 1 character long',
        'string.max': 'Option 4 must not exceed 200 characters',
        'any.required': 'Option 4 is required',
      }),
    answer: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Answer must be at least 1 character long',
        'string.max': 'Answer must not exceed 200 characters',
        'any.required': 'Answer is required',
      }),
    lessonId: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'Lesson ID must be a number',
        'number.integer': 'Lesson ID must be an integer',
        'number.positive': 'Lesson ID must be positive',
        'any.required': 'Lesson ID is required',
      }),
    explanation: Joi.string()
      .max(1000)
      .trim()
      .optional()
      .messages({
        'string.max': 'Explanation must not exceed 1000 characters',
      }),
    difficulty: Joi.string()
      .valid('easy', 'medium', 'hard')
      .default('medium')
      .messages({
        'any.only': 'Difficulty must be one of: easy, medium, hard',
      }),
    points: Joi.number()
      .integer()
      .positive()
      .max(100)
      .default(10)
      .messages({
        'number.base': 'Points must be a number',
        'number.integer': 'Points must be an integer',
        'number.positive': 'Points must be positive',
        'number.max': 'Points must not exceed 100',
      }),
  }),

  // Quiz Type Two validation
  createQuizTwo: Joi.object({
    question: Joi.string()
      .min(5)
      .max(500)
      .trim()
      .required()
      .messages({
        'string.min': 'Question must be at least 5 characters long',
        'string.max': 'Question must not exceed 500 characters',
        'any.required': 'Question is required',
      }),
    option1: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 1 must be at least 1 character long',
        'string.max': 'Option 1 must not exceed 200 characters',
        'any.required': 'Option 1 is required',
      }),
    option2: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 2 must be at least 1 character long',
        'string.max': 'Option 2 must not exceed 200 characters',
        'any.required': 'Option 2 is required',
      }),
    option3: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 3 must be at least 1 character long',
        'string.max': 'Option 3 must not exceed 200 characters',
        'any.required': 'Option 3 is required',
      }),
    option4: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 4 must be at least 1 character long',
        'string.max': 'Option 4 must not exceed 200 characters',
        'any.required': 'Option 4 is required',
      }),
    answer: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Answer must be at least 1 character long',
        'string.max': 'Answer must not exceed 200 characters',
        'any.required': 'Answer is required',
      }),
    lessonId: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'Lesson ID must be a number',
        'number.integer': 'Lesson ID must be an integer',
        'number.positive': 'Lesson ID must be positive',
        'any.required': 'Lesson ID is required',
      }),
    explanation: Joi.string()
      .max(1000)
      .trim()
      .optional()
      .messages({
        'string.max': 'Explanation must not exceed 1000 characters',
      }),
    difficulty: Joi.string()
      .valid('easy', 'medium', 'hard')
      .default('medium')
      .messages({
        'any.only': 'Difficulty must be one of: easy, medium, hard',
      }),
    points: Joi.number()
      .integer()
      .positive()
      .max(100)
      .default(10)
      .messages({
        'number.base': 'Points must be a number',
        'number.integer': 'Points must be an integer',
        'number.positive': 'Points must be positive',
        'number.max': 'Points must not exceed 100',
      }),
  }),

  // Quiz Type Three validation
  createQuizThree: Joi.object({
    question: Joi.string()
      .min(5)
      .max(500)
      .trim()
      .required()
      .messages({
        'string.min': 'Question must be at least 5 characters long',
        'string.max': 'Question must not exceed 500 characters',
        'any.required': 'Question is required',
      }),
    option1: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 1 must be at least 1 character long',
        'string.max': 'Option 1 must not exceed 200 characters',
        'any.required': 'Option 1 is required',
      }),
    option2: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 2 must be at least 1 character long',
        'string.max': 'Option 2 must not exceed 200 characters',
        'any.required': 'Option 2 is required',
      }),
    option3: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 3 must be at least 1 character long',
        'string.max': 'Option 3 must not exceed 200 characters',
        'any.required': 'Option 3 is required',
      }),
    option4: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Option 4 must be at least 1 character long',
        'string.max': 'Option 4 must not exceed 200 characters',
        'any.required': 'Option 4 is required',
      }),
    answer: Joi.string()
      .min(1)
      .max(200)
      .trim()
      .required()
      .messages({
        'string.min': 'Answer must be at least 1 character long',
        'string.max': 'Answer must not exceed 200 characters',
        'any.required': 'Answer is required',
      }),
    lessonId: Joi.number()
      .integer()
      .positive()
      .required()
      .messages({
        'number.base': 'Lesson ID must be a number',
        'number.integer': 'Lesson ID must be an integer',
        'number.positive': 'Lesson ID must be positive',
        'any.required': 'Lesson ID is required',
      }),
    explanation: Joi.string()
      .max(1000)
      .trim()
      .optional()
      .messages({
        'string.max': 'Explanation must not exceed 1000 characters',
      }),
    difficulty: Joi.string()
      .valid('easy', 'medium', 'hard')
      .default('medium')
      .messages({
        'any.only': 'Difficulty must be one of: easy, medium, hard',
      }),
    points: Joi.number()
      .integer()
      .positive()
      .max(100)
      .default(10)
      .messages({
        'number.base': 'Points must be a number',
        'number.integer': 'Points must be an integer',
        'number.positive': 'Points must be positive',
        'number.max': 'Points must not exceed 100',
      }),
  }),
};

module.exports = {
  quizSchemas,
};
