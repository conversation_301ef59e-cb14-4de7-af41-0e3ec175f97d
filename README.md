# News Lexica Backend

A Node.js backend application for an educational platform with IELTS modules, lessons, and user management.

## Technologies Used

- Node.js
- Express.js
- Prisma ORM
- MySQL Database
- JWT Authentication
- bcrypt for password hashing

## Features

- User authentication and authorization
- IELTS reading modules management
- Lesson management
- Quiz management
- User scoring system
- File uploads
- OTP verification

## Setup Instructions

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file with the following variables:
   ```
   DATABASE_URL="mysql://username:password@localhost:3306/database_name"
   PORT=5000
   ACCESS_TOKEN_SECRET=your_access_token_secret
   REFRESH_TOKEN_SECRET=your_refresh_token_secret
   ```
4. Run database migrations:
   ```
   npx prisma migrate dev
   ```
5. Seed the database (optional):
   ```
   node prisma/seed.js
   ```
6. Start the server:
   ```
   node app.js
   ```

## API Routes

### Authentication
- `POST /auth` - User login
- `POST /auth/admin` - Admin login
- `POST /auth/sendOtp` - Send OTP for verification
- `POST /auth/verifyOtp` - Verify OTP
- `POST /auth/newAccessToken` - Get new access token (v2 only)

### User Management
- `GET /user` - Get user info
- `POST /user/updateScoreById` - Update user score
- `POST /user/getScoreList` - Get all scores with users
- `GET /user/subscription` - Get subscription requests
- `POST /user/subscription` - Add subscription

### IELTS Module
- `GET /ielts/books` - Get all books
- `POST /ielts/books` - Add a book
- `GET /ielts/books/:id` - Get a specific book
- `PUT /ielts/books/:id` - Update a book
- `DELETE /ielts/books/:id` - Delete a book
- `GET /ielts/readingLists` - Get all reading lists
- `POST /ielts/readingLists` - Add a reading list
- `GET /ielts/readingPassages` - Get all reading passages
- `POST /ielts/readingPassages` - Add a reading passage

## API Versioning

The application supports API versioning:
- V1 routes: `/auth`, `/register`, `/lesson`, `/user`, `/ielts`
- V2 routes: `/api/v2/auth`, `/api/v2/register`, `/api/v2/lesson`, `/api/v2/user`, `/api/v2/ielts`

## License

ISC