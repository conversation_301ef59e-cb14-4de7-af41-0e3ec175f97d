const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  const hashedPassword = await bcrypt.hash('password123', 10); // Hash the password

  const user = await prisma.user.upsert({
    where: { phone: '018585873112' }, // Ensure idempotency
    update: {},
    create: {
      name: '<PERSON>',
      photo: 'https://fastly.picsum.photos/id/16/200/200.jpg?hmac=RGkrMlBKS58bjVd__gfGtWpmouvlXbzUHDjGHNcbIic',
      password: hashedPassword,
      phone: '01858573112',
      refreshToken: null,
      role: {
        create: {
          role: 'USER', // Adjust role based on your `Role` enum
        },
      },
    },
  });

  console.log('User seeded:', user);
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
