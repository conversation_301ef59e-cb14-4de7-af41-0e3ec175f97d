const rateLimit = require('express-rate-limit');
const config = require('../config/environment');

// General rate limiter for all requests
const generalLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.maxRequests * 10, // More lenient for general requests
  message: {
    error: 'Too many requests from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimit.windowMs / 1000 / 60) // in minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Strict rate limiter for authentication endpoints
const authLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs, // 15 minutes
  max: config.rateLimit.maxRequests, // 5 requests per window
  message: {
    error: 'Too many authentication attempts from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimit.windowMs / 1000 / 60) // in minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
  // Skip successful requests
  skipSuccessfulRequests: true,
});

// Very strict rate limiter for OTP requests
const otpLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs, // 15 minutes
  max: 3, // Only 3 OTP requests per window
  message: {
    error: 'Too many OTP requests from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimit.windowMs / 1000 / 60) // in minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Rate limiter for password reset attempts
const passwordResetLimiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: 3, // Only 3 password reset attempts per window
  message: {
    error: 'Too many password reset attempts from this IP, please try again later.',
    retryAfter: Math.ceil(config.rateLimit.windowMs / 1000 / 60) // in minutes
  },
  standardHeaders: true,
  legacyHeaders: false,
});

module.exports = {
  generalLimiter,
  authLimiter,
  otpLimiter,
  passwordResetLimiter
};
