const express = require("express");
const router = express.Router();
const verifyJWT = require("../../middleware/verifyJWT");
const { verifyRoles } = require("../../middleware/verifyRoles");
const { validateBody, validateQuery, validateParams } = require("../../middleware/validateRequest");
const Joi = require("joi");

const {
  createExamLesson,
  getExamLessons,
  getExamLessonById,
  updateExamLesson,
  deleteExamLesson,
  createExamQuiz,
  getExamQuizzesByLessonId,
  updateExamQuiz,
  deleteExamQuiz,
} = require("../../controller/v2/exam-lessons.controller");

// Validation schemas
const createExamLessonSchema = Joi.object({
  title: Joi.string().required().min(1).max(200),
  content: Joi.string().required().min(1),
  lessonNumber: Joi.number().integer().positive().required(),
});

const updateExamLessonSchema = Joi.object({
  title: Joi.string().min(1).max(200),
  content: Joi.string().min(1),
  lessonNumber: Joi.number().integer().positive(),
});

const createExamQuizSchema = Joi.object({
  question: Joi.string().required().min(1),
  option1: Joi.string().required().min(1),
  option2: Joi.string().required().min(1),
  option3: Joi.string().required().min(1),
  option4: Joi.string().required().min(1),
  answer: Joi.string().required().min(1),
  lessonId: Joi.string().required(),
});

const updateExamQuizSchema = Joi.object({
  question: Joi.string().min(1),
  option1: Joi.string().min(1),
  option2: Joi.string().min(1),
  option3: Joi.string().min(1),
  option4: Joi.string().min(1),
  answer: Joi.string().min(1),
});

const paginationSchema = Joi.object({
  page: Joi.number().integer().positive().default(1),
  limit: Joi.number().integer().positive().max(100).default(10),
});

const idSchema = Joi.object({
  id: Joi.string().required(),
});

const lessonIdSchema = Joi.object({
  lessonId: Joi.string().required(),
});

// Exam Lessons Routes
/**
 * @route   GET /api/v2/exam-lessons
 * @desc    Get all exam lessons with pagination
 * @access  Public
 * @query   ?page=1&limit=10
 */
router.get("/", validateQuery(paginationSchema), getExamLessons);

/**
 * @route   POST /api/v2/exam-lessons
 * @desc    Create a new exam lesson
 * @access  Private (Admin only)
 */
router.post(
  "/",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateBody(createExamLessonSchema),
  createExamLesson
);

/**
 * @route   GET /api/v2/exam-lessons/:id
 * @desc    Get a specific exam lesson by ID
 * @access  Public
 */
router.get("/:id", validateParams(idSchema), getExamLessonById);

/**
 * @route   PUT /api/v2/exam-lessons/:id
 * @desc    Update a specific exam lesson
 * @access  Private (Admin only)
 */
router.put(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(idSchema),
  validateBody(updateExamLessonSchema),
  updateExamLesson
);

/**
 * @route   DELETE /api/v2/exam-lessons/:id
 * @desc    Delete a specific exam lesson
 * @access  Private (Admin only)
 */
router.delete(
  "/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(idSchema),
  deleteExamLesson
);

// Exam Quizzes Routes
/**
 * @route   GET /api/v2/exam-lessons/:lessonId/quizzes
 * @desc    Get all quizzes for a specific exam lesson
 * @access  Public
 */
router.get(
  "/:lessonId/quizzes",
  validateParams(lessonIdSchema),
  getExamQuizzesByLessonId
);

/**
 * @route   POST /api/v2/exam-lessons/:lessonId/quizzes
 * @desc    Create a new quiz for an exam lesson
 * @access  Private (Admin only)
 */
router.post(
  "/:lessonId/quizzes",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(lessonIdSchema),
  validateBody(createExamQuizSchema),
  (req, res, next) => {
    // Add lessonId from params to body
    req.body.lessonId = req.params.lessonId;
    next();
  },
  createExamQuiz
);

/**
 * @route   PUT /api/v2/exam-lessons/:lessonId/quizzes/:id
 * @desc    Update a specific exam quiz
 * @access  Private (Admin only)
 */
router.put(
  "/:lessonId/quizzes/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(Joi.object({
    lessonId: Joi.string().required(),
    id: Joi.string().required(),
  })),
  validateBody(updateExamQuizSchema),
  updateExamQuiz
);

/**
 * @route   DELETE /api/v2/exam-lessons/:lessonId/quizzes/:id
 * @desc    Delete a specific exam quiz
 * @access  Private (Admin only)
 */
router.delete(
  "/:lessonId/quizzes/:id",
  verifyJWT,
  verifyRoles("ADMIN"),
  validateParams(Joi.object({
    lessonId: Joi.string().required(),
    id: Joi.string().required(),
  })),
  deleteExamQuiz
);

module.exports = router;
