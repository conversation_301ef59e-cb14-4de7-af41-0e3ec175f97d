const express = require("express");
const { handleLogin, sendOtp, verifyOtp,testServer,newAccessToken,handleLoginAdmin } = require("../../controller/v2/authentication_controller");
const router = express.Router();

router.get("/testServer", testServer);
router.post("/", handleLogin);
router.post("/admin", handleLoginAdmin);
router.post("/sendOtp", sendOtp);
router.post("/verifyOtp", verifyOtp);
router.post("/newAccessToken", newAccessToken);
module.exports = router;