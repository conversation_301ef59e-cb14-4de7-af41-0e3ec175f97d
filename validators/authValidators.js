const Joi = require("joi");

// Phone number validation (Bangladesh format)
const phoneSchema = Joi.string()
  .pattern(/^01[3-9]\d{8}$/)
  .required()
  .messages({
    "string.pattern.base":
      "Phone number must be a valid Bangladesh mobile number (01XXXXXXXXX)",
    "any.required": "Phone number is required",
  });

// Password validation
const passwordSchema = Joi.string().min(6).max(128).required().messages({
  "string.min": "Password must be at least 6 characters long",
  "string.max": "Password must not exceed 128 characters",
  "any.required": "Password is required",
});

// OTP validation
const otpSchema = Joi.string()
  .pattern(/^\d{4}$/)
  .required()
  .messages({
    "string.pattern.base": "OTP must be a 4-digit number",
    "any.required": "OTP is required",
  });

// Name validation
const nameSchema = Joi.string().min(2).max(100).trim().required().messages({
  "string.min": "Name must be at least 2 characters long",
  "string.max": "Name must not exceed 100 characters",
  "any.required": "Name is required",
});

// Login validation schema
const loginSchema = Joi.object({
  phone: phoneSchema,
  password: passwordSchema,
  token: Joi.string().optional(), // Firebase token or similar
});

// Registration validation schema
const registerSchema = Joi.object({
  name: nameSchema,
  phone: phoneSchema,
  password: passwordSchema,
});

// OTP request validation schema
const otpRequestSchema = Joi.object({
  phone: phoneSchema,
});

// OTP verification validation schema
const otpVerificationSchema = Joi.object({
  phone: phoneSchema,
  enteredOtp: otpSchema,
  hashedOtp: Joi.string().required().messages({
    "any.required": "Hashed OTP is required",
  }),
});

// Password change validation schema
const passwordChangeSchema = Joi.object({
  phone: phoneSchema,
  currentPassword: passwordSchema,
  newPassword: passwordSchema,
});

// Refresh token validation schema
const refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    "any.required": "Refresh token is required",
  }),
});

module.exports = {
  loginSchema,
  registerSchema,
  otpRequestSchema,
  otpVerificationSchema,
  passwordChangeSchema,
  refreshTokenSchema,
};
