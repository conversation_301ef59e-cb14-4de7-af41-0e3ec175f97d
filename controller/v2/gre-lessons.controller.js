const prisma = require("../../prisma/index");

/**
 * GRE Lessons Controller
 * Handles CRUD operations for GRE lessons and their quizzes
 */

// GRE Lessons
const createGreLesson = async (req, res) => {
  try {
    const { title, content, lessonNumber } = req.body;

    const lesson = await prisma.greLesson.create({
      data: {
        title,
        content,
        lessonNumber: parseInt(lessonNumber),
      },
    });

    res.status(201).json({
      success: true,
      message: "GRE lesson created successfully",
      data: lesson,
    });
  } catch (error) {
    console.error("Error creating GRE lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create GRE lesson",
      error: error.message,
    });
  }
};

const getGreLessons = async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    const [lessons, totalCount] = await Promise.all([
      prisma.greLesson.findMany({
        include: {
          GreQuizes: true,
        },
        orderBy: { createdAt: 'desc' },
        skip,
        take: limitNum,
      }),
      prisma.greLesson.count(),
    ]);

    res.status(200).json({
      success: true,
      data: lessons,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
      },
    });
  } catch (error) {
    console.error("Error fetching GRE lessons:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch GRE lessons",
      error: error.message,
    });
  }
};

const getGreLessonById = async (req, res) => {
  try {
    const { id } = req.params;

    const lesson = await prisma.greLesson.findUnique({
      where: { id },
      include: {
        GreQuizes: true,
      },
    });

    if (!lesson) {
      return res.status(404).json({
        success: false,
        message: "GRE lesson not found",
      });
    }

    res.status(200).json({
      success: true,
      data: lesson,
    });
  } catch (error) {
    console.error("Error fetching GRE lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch GRE lesson",
      error: error.message,
    });
  }
};

const updateGreLesson = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, lessonNumber } = req.body;

    const lesson = await prisma.greLesson.update({
      where: { id },
      data: {
        title,
        content,
        lessonNumber: parseInt(lessonNumber),
      },
    });

    res.status(200).json({
      success: true,
      message: "GRE lesson updated successfully",
      data: lesson,
    });
  } catch (error) {
    console.error("Error updating GRE lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update GRE lesson",
      error: error.message,
    });
  }
};

const deleteGreLesson = async (req, res) => {
  try {
    const { id } = req.params;

    // First delete all related quizzes
    await prisma.greQuizes.deleteMany({
      where: { lessonId: id },
    });

    // Then delete the lesson
    await prisma.greLesson.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "GRE lesson and related quizzes deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting GRE lesson:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete GRE lesson",
      error: error.message,
    });
  }
};

// GRE Quizzes
const createGreQuiz = async (req, res) => {
  try {
    const { question, option1, option2, option3, option4, option5, answer, lessonId } = req.body;

    const quiz = await prisma.greQuizes.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        option5,
        answer,
        lessonId,
      },
    });

    res.status(201).json({
      success: true,
      message: "GRE quiz created successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error creating GRE quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create GRE quiz",
      error: error.message,
    });
  }
};

const getGreQuizzesByLessonId = async (req, res) => {
  try {
    const { lessonId } = req.params;

    const quizzes = await prisma.greQuizes.findMany({
      where: { lessonId },
    });

    res.status(200).json({
      success: true,
      data: quizzes,
    });
  } catch (error) {
    console.error("Error fetching GRE quizzes:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch GRE quizzes",
      error: error.message,
    });
  }
};

const updateGreQuiz = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, option5, answer } = req.body;

    const quiz = await prisma.greQuizes.update({
      where: { id },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        option5,
        answer,
      },
    });

    res.status(200).json({
      success: true,
      message: "GRE quiz updated successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error updating GRE quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update GRE quiz",
      error: error.message,
    });
  }
};

const deleteGreQuiz = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.greQuizes.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "GRE quiz deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting GRE quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete GRE quiz",
      error: error.message,
    });
  }
};

module.exports = {
  // GRE Lessons
  createGreLesson,
  getGreLessons,
  getGreLessonById,
  updateGreLesson,
  deleteGreLesson,
  
  // GRE Quizzes
  createGreQuiz,
  getGreQuizzesByLessonId,
  updateGreQuiz,
  deleteGreQuiz,
};
