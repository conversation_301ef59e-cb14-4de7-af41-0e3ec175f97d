const prisma = require("../../prisma/index");

/**
 * Quizzes Controller
 * Handles CRUD operations for lesson quizzes (QuizOne, QuizTwo, QuizThree)
 */

// QuizOne Controllers
const createQuizOne = async (req, res) => {
  try {
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    const quiz = await prisma.quizOne.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId: parseInt(lessonId),
      },
    });

    res.status(201).json({
      success: true,
      message: "Quiz created successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error creating quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create quiz",
      error: error.message,
    });
  }
};

const getQuizzesByLessonId = async (req, res) => {
  try {
    const { lessonId } = req.params;
    const { type } = req.query; // quizOne, quizTwo, quizThree

    let quizzes;
    switch (type) {
      case 'quizTwo':
        quizzes = await prisma.quizTwo.findMany({
          where: { lessonId: parseInt(lessonId) },
        });
        break;
      case 'quizThree':
        quizzes = await prisma.quizThree.findMany({
          where: { lessonId: parseInt(lessonId) },
        });
        break;
      default:
        quizzes = await prisma.quizOne.findMany({
          where: { lessonId: parseInt(lessonId) },
        });
    }

    res.status(200).json({
      success: true,
      data: quizzes,
    });
  } catch (error) {
    console.error("Error fetching quizzes:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch quizzes",
      error: error.message,
    });
  }
};

const updateQuizOne = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, answer } = req.body;

    const quiz = await prisma.quizOne.update({
      where: { id },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
      },
    });

    res.status(200).json({
      success: true,
      message: "Quiz updated successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error updating quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update quiz",
      error: error.message,
    });
  }
};

const deleteQuizOne = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.quizOne.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Quiz deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting quiz:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete quiz",
      error: error.message,
    });
  }
};

// QuizTwo Controllers
const createQuizTwo = async (req, res) => {
  try {
    const { question, answer, lessonId } = req.body;

    const quiz = await prisma.quizTwo.create({
      data: {
        question,
        answer,
        lessonId: parseInt(lessonId),
      },
    });

    res.status(201).json({
      success: true,
      message: "Quiz Two created successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error creating quiz two:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create quiz two",
      error: error.message,
    });
  }
};

const updateQuizTwo = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, answer } = req.body;

    const quiz = await prisma.quizTwo.update({
      where: { id },
      data: {
        question,
        answer,
      },
    });

    res.status(200).json({
      success: true,
      message: "Quiz Two updated successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error updating quiz two:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update quiz two",
      error: error.message,
    });
  }
};

const deleteQuizTwo = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.quizTwo.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Quiz Two deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting quiz two:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete quiz two",
      error: error.message,
    });
  }
};

// QuizThree Controllers
const createQuizThree = async (req, res) => {
  try {
    const { question, option1, option2, option3, option4, answer, lessonId } = req.body;

    const quiz = await prisma.quizThree.create({
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
        lessonId: parseInt(lessonId),
      },
    });

    res.status(201).json({
      success: true,
      message: "Quiz Three created successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error creating quiz three:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create quiz three",
      error: error.message,
    });
  }
};

const updateQuizThree = async (req, res) => {
  try {
    const { id } = req.params;
    const { question, option1, option2, option3, option4, answer } = req.body;

    const quiz = await prisma.quizThree.update({
      where: { id },
      data: {
        question,
        option1,
        option2,
        option3,
        option4,
        answer,
      },
    });

    res.status(200).json({
      success: true,
      message: "Quiz Three updated successfully",
      data: quiz,
    });
  } catch (error) {
    console.error("Error updating quiz three:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update quiz three",
      error: error.message,
    });
  }
};

const deleteQuizThree = async (req, res) => {
  try {
    const { id } = req.params;

    await prisma.quizThree.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Quiz Three deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting quiz three:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete quiz three",
      error: error.message,
    });
  }
};

module.exports = {
  // QuizOne
  createQuizOne,
  updateQuizOne,
  deleteQuizOne,
  
  // QuizTwo
  createQuizTwo,
  updateQuizTwo,
  deleteQuizTwo,
  
  // QuizThree
  createQuizThree,
  updateQuizThree,
  deleteQuizThree,
  
  // Common
  getQuizzesByLessonId,
};
