# Test Environment Configuration
NODE_ENV=test

# Database Configuration (use a separate test database)
DATABASE_URL="mysql://root:71110921@localhost:3306/news_lexica_test"

# Server Configuration
PORT=5001

# JWT Configuration (use different secrets for testing)
ACCESS_TOKEN_SECRET=test_access_token_secret_for_testing_only
REFRESH_TOKEN_SECRET=test_refresh_token_secret_for_testing_only
ACCESS_TOKEN_EXPIRY=1h
REFRESH_TOKEN_EXPIRY=7d

# SMS API Configuration (mock values for testing)
SMS_API_KEY=test_sms_api_key
SMS_SENDER_ID=test_sender_id
SMS_API_URL=http://test-sms-api.com

# Test Configuration
TEST_PHONE_NUMBER=01712345678
TEST_OTP=1234

# Rate Limiting Configuration (more lenient for testing)
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Configuration
MAX_FILE_SIZE_MB=5
ALLOWED_AUDIO_TYPES=audio/mpeg,audio/mp3,audio/wav,audio/ogg,audio/mp4
