const request = require('supertest');
const express = require('express');

// Mock Prisma
const mockPrisma = {
  lesson: {
    create: jest.fn(),
    findMany: jest.fn(),
    findUnique: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  quizOne: {
    deleteMany: jest.fn(),
  },
  quizTwo: {
    deleteMany: jest.fn(),
  },
  quizThree: {
    deleteMany: jest.fn(),
  },
};

jest.mock('../../../prisma/index', () => mockPrisma);

const {
  createLesson,
  getLessons,
  getLessonById,
  updateLesson,
  deleteLesson,
} = require('../../../controller/v2/lessons.controller');

describe('Lessons Controller', () => {
  let app;

  beforeEach(() => {
    app = express();
    app.use(express.json());
    jest.clearAllMocks();
  });

  describe('createLesson', () => {
    it('should create a new lesson successfully', async () => {
      const mockLesson = {
        id: 1,
        lessonTitle: 'Test Lesson',
        title: 'Test Article',
        lessonNumber: 1,
        paragraph: 'Test content',
        part: 1,
        pressName: 'Test Press',
        publishDate: '2024-01-01',
        contentType: 1,
        category: 'news',
      };

      mockPrisma.lesson.create.mockResolvedValue(mockLesson);

      app.post('/lessons', createLesson);

      const response = await request(app)
        .post('/lessons')
        .send(mockLesson);

      expect(response.status).toBe(201);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockLesson);
      expect(mockPrisma.lesson.create).toHaveBeenCalledWith({
        data: expect.objectContaining({
          lessonTitle: 'Test Lesson',
          lessonNumber: 1,
        }),
      });
    });

    it('should handle creation errors', async () => {
      mockPrisma.lesson.create.mockRejectedValue(new Error('Database error'));

      app.post('/lessons', createLesson);

      const response = await request(app)
        .post('/lessons')
        .send(global.testUtils.createTestLesson());

      expect(response.status).toBe(500);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Failed to create lesson');
    });
  });

  describe('getLessons', () => {
    it('should get lessons with pagination', async () => {
      const mockLessons = [
        { id: 1, title: 'Lesson 1', category: 'news' },
        { id: 2, title: 'Lesson 2', category: 'sports' },
      ];

      mockPrisma.lesson.findMany.mockResolvedValue(mockLessons);
      mockPrisma.lesson.count.mockResolvedValue(2);

      app.get('/lessons', getLessons);

      const response = await request(app)
        .get('/lessons')
        .query({ page: 1, limit: 10 });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockLessons);
      expect(response.body.pagination).toEqual({
        page: 1,
        limit: 10,
        total: 2,
        totalPages: 1,
      });
    });

    it('should filter lessons by category', async () => {
      const mockLessons = [
        { id: 1, title: 'News Lesson', category: 'news' },
      ];

      mockPrisma.lesson.findMany.mockResolvedValue(mockLessons);
      mockPrisma.lesson.count.mockResolvedValue(1);

      app.get('/lessons', getLessons);

      const response = await request(app)
        .get('/lessons')
        .query({ category: 'news' });

      expect(response.status).toBe(200);
      expect(mockPrisma.lesson.findMany).toHaveBeenCalledWith({
        where: { category: 'news' },
        select: expect.any(Object),
        orderBy: { createdAt: 'desc' },
        skip: 0,
        take: 10,
      });
    });
  });

  describe('getLessonById', () => {
    it('should get lesson by ID with quizzes', async () => {
      const mockLesson = {
        id: 1,
        title: 'Test Lesson',
        QuizOne: [],
        QuizTwo: [],
        QuizThree: [],
      };

      mockPrisma.lesson.findUnique.mockResolvedValue(mockLesson);

      app.get('/lessons/:id', getLessonById);

      const response = await request(app).get('/lessons/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(mockLesson);
      expect(mockPrisma.lesson.findUnique).toHaveBeenCalledWith({
        where: { id: 1 },
        include: {
          QuizOne: true,
          QuizTwo: true,
          QuizThree: true,
        },
      });
    });

    it('should return 404 for non-existent lesson', async () => {
      mockPrisma.lesson.findUnique.mockResolvedValue(null);

      app.get('/lessons/:id', getLessonById);

      const response = await request(app).get('/lessons/999');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Lesson not found');
    });
  });

  describe('updateLesson', () => {
    it('should update lesson successfully', async () => {
      const existingLesson = { id: 1, title: 'Old Title' };
      const updatedLesson = { id: 1, title: 'New Title' };

      mockPrisma.lesson.findUnique.mockResolvedValue(existingLesson);
      mockPrisma.lesson.update.mockResolvedValue(updatedLesson);

      app.put('/lessons/:id', updateLesson);

      const response = await request(app)
        .put('/lessons/1')
        .send({ title: 'New Title' });

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.data).toEqual(updatedLesson);
    });

    it('should return 404 for non-existent lesson', async () => {
      mockPrisma.lesson.findUnique.mockResolvedValue(null);

      app.put('/lessons/:id', updateLesson);

      const response = await request(app)
        .put('/lessons/999')
        .send({ title: 'New Title' });

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Lesson not found');
    });
  });

  describe('deleteLesson', () => {
    it('should delete lesson and related quizzes', async () => {
      const existingLesson = { id: 1, title: 'Test Lesson' };

      mockPrisma.lesson.findUnique.mockResolvedValue(existingLesson);
      mockPrisma.quizOne.deleteMany.mockResolvedValue({ count: 2 });
      mockPrisma.quizTwo.deleteMany.mockResolvedValue({ count: 1 });
      mockPrisma.quizThree.deleteMany.mockResolvedValue({ count: 3 });
      mockPrisma.lesson.delete.mockResolvedValue(existingLesson);

      app.delete('/lessons/:id', deleteLesson);

      const response = await request(app).delete('/lessons/1');

      expect(response.status).toBe(200);
      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Lesson deleted successfully');
      
      // Verify that related quizzes were deleted first
      expect(mockPrisma.quizOne.deleteMany).toHaveBeenCalledWith({
        where: { lessonId: 1 },
      });
      expect(mockPrisma.quizTwo.deleteMany).toHaveBeenCalledWith({
        where: { lessonId: 1 },
      });
      expect(mockPrisma.quizThree.deleteMany).toHaveBeenCalledWith({
        where: { lessonId: 1 },
      });
      expect(mockPrisma.lesson.delete).toHaveBeenCalledWith({
        where: { id: 1 },
      });
    });

    it('should return 404 for non-existent lesson', async () => {
      mockPrisma.lesson.findUnique.mockResolvedValue(null);

      app.delete('/lessons/:id', deleteLesson);

      const response = await request(app).delete('/lessons/999');

      expect(response.status).toBe(404);
      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Lesson not found');
    });
  });
});
