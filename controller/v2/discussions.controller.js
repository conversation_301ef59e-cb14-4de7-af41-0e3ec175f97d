const prisma = require("../../prisma/index");

/**
 * Discussions Controller
 * Handles lesson discussions and voting functionality
 */

const createDiscussion = async (req, res) => {
  try {
    const { lessonId, comment, parentId } = req.body;
    const userId = req.user; // From JWT middleware

    const discussion = await prisma.lessonDiscussion.create({
      data: {
        lessonId: parseInt(lessonId),
        userId: parseInt(userId),
        comment,
        parentId: parentId ? parentId : null,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            photo: true,
          },
        },
        votes: true,
      },
    });

    res.status(201).json({
      success: true,
      message: "Discussion created successfully",
      data: discussion,
    });
  } catch (error) {
    console.error("Error creating discussion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to create discussion",
      error: error.message,
    });
  }
};

const getDiscussionsByLessonId = async (req, res) => {
  try {
    const { lessonId } = req.params;
    const { page = 1, limit = 10 } = req.query;

    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    if (pageNum < 1 || limitNum < 1) {
      return res.status(400).json({
        success: false,
        message: "Invalid pagination parameters",
      });
    }

    const [discussions, totalCount] = await Promise.all([
      prisma.lessonDiscussion.findMany({
        where: { 
          lessonId: parseInt(lessonId), 
          parentId: null // Only main discussions
        },
        include: { 
          user: {
            select: {
              id: true,
              name: true,
              photo: true,
            },
          },
          votes: true,
          replies: {
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  photo: true,
                },
              },
              votes: true,
            },
          },
        },
        orderBy: { createdAt: "desc" },
        skip,
        take: limitNum,
      }),
      prisma.lessonDiscussion.count({
        where: { lessonId: parseInt(lessonId), parentId: null },
      }),
    ]);

    // Calculate vote scores for each discussion
    const discussionsWithScores = discussions.map(discussion => {
      const upvotes = discussion.votes.filter(vote => vote.voteType === 'UPVOTE').length;
      const downvotes = discussion.votes.filter(vote => vote.voteType === 'DOWNVOTE').length;
      const score = upvotes - downvotes;

      const repliesWithScores = discussion.replies.map(reply => {
        const replyUpvotes = reply.votes.filter(vote => vote.voteType === 'UPVOTE').length;
        const replyDownvotes = reply.votes.filter(vote => vote.voteType === 'DOWNVOTE').length;
        const replyScore = replyUpvotes - replyDownvotes;

        return {
          ...reply,
          score: replyScore,
          upvotes: replyUpvotes,
          downvotes: replyDownvotes,
        };
      });

      return {
        ...discussion,
        score,
        upvotes,
        downvotes,
        replies: repliesWithScores,
      };
    });

    res.status(200).json({
      success: true,
      data: discussionsWithScores,
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limitNum),
      },
    });
  } catch (error) {
    console.error("Error fetching discussions:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch discussions",
      error: error.message,
    });
  }
};

const voteOnDiscussion = async (req, res) => {
  try {
    const { discussionId, voteType } = req.body;
    const userId = req.user; // From JWT middleware

    if (!['UPVOTE', 'DOWNVOTE'].includes(voteType)) {
      return res.status(400).json({
        success: false,
        message: "Invalid vote type. Must be UPVOTE or DOWNVOTE",
      });
    }

    // Check if user has already voted on this discussion
    const existingVote = await prisma.vote.findUnique({
      where: {
        userId_discussionId: {
          userId: parseInt(userId),
          discussionId: discussionId,
        },
      },
    });

    if (existingVote) {
      if (existingVote.voteType === voteType) {
        // Remove vote if same type
        await prisma.vote.delete({
          where: {
            userId_discussionId: {
              userId: parseInt(userId),
              discussionId: discussionId,
            },
          },
        });

        return res.status(200).json({
          success: true,
          message: "Vote removed successfully",
          action: "removed",
        });
      } else {
        // Update vote if different type
        await prisma.vote.update({
          where: {
            userId_discussionId: {
              userId: parseInt(userId),
              discussionId: discussionId,
            },
          },
          data: {
            voteType: voteType,
          },
        });

        return res.status(200).json({
          success: true,
          message: "Vote updated successfully",
          action: "updated",
        });
      }
    } else {
      // Create new vote
      await prisma.vote.create({
        data: {
          userId: parseInt(userId),
          discussionId: discussionId,
          voteType: voteType,
        },
      });

      return res.status(201).json({
        success: true,
        message: "Vote created successfully",
        action: "created",
      });
    }
  } catch (error) {
    console.error("Error voting on discussion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to vote on discussion",
      error: error.message,
    });
  }
};

const updateDiscussion = async (req, res) => {
  try {
    const { id } = req.params;
    const { comment } = req.body;
    const userId = req.user; // From JWT middleware

    // Check if discussion exists and belongs to user
    const existingDiscussion = await prisma.lessonDiscussion.findUnique({
      where: { id },
    });

    if (!existingDiscussion) {
      return res.status(404).json({
        success: false,
        message: "Discussion not found",
      });
    }

    if (existingDiscussion.userId !== parseInt(userId)) {
      return res.status(403).json({
        success: false,
        message: "You can only edit your own discussions",
      });
    }

    const discussion = await prisma.lessonDiscussion.update({
      where: { id },
      data: { comment },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            photo: true,
          },
        },
        votes: true,
      },
    });

    res.status(200).json({
      success: true,
      message: "Discussion updated successfully",
      data: discussion,
    });
  } catch (error) {
    console.error("Error updating discussion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to update discussion",
      error: error.message,
    });
  }
};

const deleteDiscussion = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user; // From JWT middleware

    // Check if discussion exists and belongs to user
    const existingDiscussion = await prisma.lessonDiscussion.findUnique({
      where: { id },
    });

    if (!existingDiscussion) {
      return res.status(404).json({
        success: false,
        message: "Discussion not found",
      });
    }

    if (existingDiscussion.userId !== parseInt(userId)) {
      return res.status(403).json({
        success: false,
        message: "You can only delete your own discussions",
      });
    }

    // Delete all votes and replies first
    await prisma.vote.deleteMany({
      where: { discussionId: id },
    });

    await prisma.lessonDiscussion.deleteMany({
      where: { parentId: id },
    });

    // Delete the discussion
    await prisma.lessonDiscussion.delete({
      where: { id },
    });

    res.status(200).json({
      success: true,
      message: "Discussion deleted successfully",
    });
  } catch (error) {
    console.error("Error deleting discussion:", error);
    res.status(500).json({
      success: false,
      message: "Failed to delete discussion",
      error: error.message,
    });
  }
};

module.exports = {
  createDiscussion,
  getDiscussionsByLessonId,
  voteOnDiscussion,
  updateDiscussion,
  deleteDiscussion,
};
