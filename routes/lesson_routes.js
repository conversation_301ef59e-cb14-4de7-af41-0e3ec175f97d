const express = require("express");
const router = express.Router();
const { addSingle<PERSON>esson, getAllLesson, getLessonDetailsData,getExamQuizListByLesson,getAllPromotion,addPromotion,getAllLessonByPage,
    addQuizOne,editQuizOne,deleteQuizOne,addQuizTwo,editQuizTwo,deleteQuizTwo,addQuizThree,editQuizThree,deleteQuizThree,
    getAllQuizOneByLessonId,getAllQuizTwoByLessonId,getAllQuizThreeByLessonId,getGreQuizListByLesson,addQuizDiscussion,getQuizDiscussion,
    addGreQuiz,editGreQuiz,deleteGreQuiz,addExamQuiz,editExamQuiz,deleteExamQuiz,getAllExamQuizByLessonId,getAllGreQuizesByLessonId } = require("../controller/lesson_controller");
router.post("/addLesson", addSingleLesson);
router.get("/getAllLesson", getAllLesson);
router.post("/addPromotion", addPromotion);
router.post("/getAllPromotion", getAllPromotion);
router.get("/getLessonDetails", getLessonDetailsData);
router.get("/getExamQuizes", getExamQuizListByLesson);
router.get("/getGreQuizListByLesson", getGreQuizListByLesson);
router.get("/getAllLessonByPage", getAllLessonByPage);


router.post('/quizOne', addQuizOne);
router.put('/quizOne/:id', editQuizOne);
router.delete('/quizOne/:id', deleteQuizOne);
router.get('/quizOne/lessons/:lessonId', getAllQuizOneByLessonId);



router.post('/quizTwo', addQuizTwo);
router.put('/quizTwo/:id', editQuizTwo);
router.delete('/quizTwo/:id', deleteQuizTwo);
router.get('/quizTwo/lesson/:lessonId', getAllQuizTwoByLessonId);



router.post('/quizThree', addQuizThree);
router.put('/quizThree/:id', editQuizThree);
router.delete('/quizThree/:id', deleteQuizThree);
router.get('/quizThree/lesson/:lessonId', getAllQuizThreeByLessonId);

router.post("/addQuizDiscussion", addQuizDiscussion);
router.get("/getQuizDiscussion", getQuizDiscussion);




router.post('/greQuiz', addGreQuiz);
router.put('/greQuiz/:id', editGreQuiz);
router.delete('/greQuiz/:id', deleteGreQuiz);
router.get('/greQuiz/lesson/:lessonId', getAllGreQuizesByLessonId);


router.post('/examQuiz', addExamQuiz);
router.put('/examQuiz/:id', editExamQuiz);
router.delete('/examQuiz/:id', deleteExamQuiz);
router.get('/examQuiz/lesson/:lessonId', getAllExamQuizByLessonId);





module.exports = router;